'use client';

import React, { useState, useEffect } from 'react';
import { 
  Target, 
  Plus, 
  Calendar, 
  Clock, 
  TrendingUp, 
  CheckCircle, 
  AlertCircle,
  Filter,
  Search,
  Edit,
  Trash2,
  Play,
  Pause,
  BarChart3,
  Award,
  Flame
} from 'lucide-react';

interface LearningGoal {
  id: string;
  title: string;
  description: string;
  type: string;
  category: string;
  status: string;
  priority: string;
  target_criteria: {
    type: string;
    target_value: number | string;
    current_value?: number | string;
    unit?: string;
  };
  start_date: string;
  target_date: string;
  completed_at?: string;
  progress_percentage: number;
  streak_count: number;
  milestones?: {
    id: string;
    title: string;
    target_value: number;
    completed: boolean;
    target_date: string;
  }[];
  learning_path?: {
    id: string;
    title: string;
  };
  course?: {
    id: string;
    title: string;
  };
}

interface GoalAnalytics {
  total_goals: number;
  active_goals: number;
  completed_goals: number;
  overdue_goals: number;
  completion_rate: number;
  average_completion_time_days: number;
  goals_by_category: { [key: string]: number };
  goals_by_priority: { [key: string]: number };
  streak_data: {
    current_streak: number;
    longest_streak: number;
  };
  upcoming_deadlines: {
    goal_id: string;
    title: string;
    target_date: string;
    days_remaining: number;
  }[];
}

export const GoalsManagementInterface: React.FC = () => {
  const [goals, setGoals] = useState<LearningGoal[]>([]);
  const [analytics, setAnalytics] = useState<GoalAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'active' | 'completed' | 'all'>('active');
  const [filters, setFilters] = useState({
    category: '',
    priority: '',
    search: '',
    due_soon: false,
    overdue: false,
  });
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    fetchGoals();
    fetchAnalytics();
  }, [filters, activeTab]);

  const fetchGoals = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      
      // Add status filter based on active tab
      if (activeTab === 'active') {
        queryParams.append('status', 'active');
      } else if (activeTab === 'completed') {
        queryParams.append('status', 'completed');
      }
      
      // Add other filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value.toString());
      });

      const response = await fetch(`/api/learning-goals?${queryParams}`);
      const data = await response.json();
      setGoals(data);
    } catch (error) {
      console.error('Error fetching goals:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalytics = async () => {
    try {
      const response = await fetch('/api/learning-goals/analytics');
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
    }
  };

  const updateGoalProgress = async (goalId: string, progressValue: number, notes?: string) => {
    try {
      const response = await fetch(`/api/learning-goals/${goalId}/progress`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          progress_value: progressValue,
          notes,
          entry_type: 'manual',
        }),
      });

      if (response.ok) {
        fetchGoals();
        fetchAnalytics();
      }
    } catch (error) {
      console.error('Error updating goal progress:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDaysRemaining = (targetDate: string) => {
    const target = new Date(targetDate);
    const now = new Date();
    const diffTime = target.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const renderAnalyticsCards = () => {
    if (!analytics) return null;

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Target className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Goals</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.active_goals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completion Rate</p>
              <p className="text-2xl font-bold text-gray-900">{Math.round(analytics.completion_rate)}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Flame className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Current Streak</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.streak_data.current_streak}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-gray-900">{analytics.overdue_goals}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderGoalCard = (goal: LearningGoal) => {
    const daysRemaining = getDaysRemaining(goal.target_date);
    const isOverdue = daysRemaining < 0 && goal.status === 'active';
    const isDueSoon = daysRemaining <= 7 && daysRemaining >= 0 && goal.status === 'active';

    return (
      <div key={goal.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{goal.title}</h3>
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{goal.description}</p>
              
              <div className="flex flex-wrap gap-2 mb-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(goal.priority)}`}>
                  {goal.priority}
                </span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(goal.status)}`}>
                  {goal.status}
                </span>
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  {goal.category.replace('_', ' ')}
                </span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              {goal.streak_count > 0 && (
                <div className="flex items-center space-x-1 text-orange-600">
                  <Flame className="w-4 h-4" />
                  <span className="text-sm font-medium">{goal.streak_count}</span>
                </div>
              )}
            </div>
          </div>

          {/* Progress bar */}
          <div className="mb-4">
            <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
              <span>Progress</span>
              <span>{Math.round(goal.progress_percentage)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${
                  goal.status === 'completed' ? 'bg-green-600' : 'bg-blue-600'
                }`}
                style={{ width: `${goal.progress_percentage}%` }}
              />
            </div>
          </div>

          {/* Target and current values */}
          <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
            <div>
              <span className="font-medium">Current: </span>
              {goal.target_criteria.current_value || 0} {goal.target_criteria.unit}
            </div>
            <div>
              <span className="font-medium">Target: </span>
              {goal.target_criteria.target_value} {goal.target_criteria.unit}
            </div>
          </div>

          {/* Milestones */}
          {goal.milestones && goal.milestones.length > 0 && (
            <div className="mb-4">
              <div className="text-sm font-medium text-gray-700 mb-2">Milestones</div>
              <div className="space-y-1">
                {goal.milestones.slice(0, 3).map(milestone => (
                  <div key={milestone.id} className="flex items-center space-x-2 text-sm">
                    <div className={`w-2 h-2 rounded-full ${
                      milestone.completed ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                    <span className={milestone.completed ? 'text-green-700 line-through' : 'text-gray-600'}>
                      {milestone.title}
                    </span>
                  </div>
                ))}
                {goal.milestones.length > 3 && (
                  <div className="text-xs text-gray-500">
                    +{goal.milestones.length - 3} more milestones
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Timeline */}
          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>Due {new Date(goal.target_date).toLocaleDateString()}</span>
            </div>
            
            <div className={`flex items-center space-x-1 ${
              isOverdue ? 'text-red-600' : isDueSoon ? 'text-orange-600' : 'text-gray-500'
            }`}>
              <Clock className="w-4 h-4" />
              <span>
                {isOverdue 
                  ? `${Math.abs(daysRemaining)} days overdue`
                  : `${daysRemaining} days left`
                }
              </span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                <Edit className="w-4 h-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                <Trash2 className="w-4 h-4" />
              </button>
              <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                <BarChart3 className="w-4 h-4" />
              </button>
            </div>
            
            <div className="flex space-x-2">
              {goal.status === 'active' && (
                <button 
                  onClick={() => {
                    const newValue = prompt('Enter progress value:', goal.target_criteria.current_value?.toString() || '0');
                    if (newValue !== null) {
                      updateGoalProgress(goal.id, parseFloat(newValue));
                    }
                  }}
                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors"
                >
                  Update Progress
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderFilters = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-64">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search goals..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <select
          value={filters.category}
          onChange={(e) => setFilters({ ...filters, category: e.target.value })}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Categories</option>
          <option value="study_time">Study Time</option>
          <option value="course_completion">Course Completion</option>
          <option value="assessment_score">Assessment Score</option>
          <option value="skill_mastery">Skill Mastery</option>
        </select>
        
        <select
          value={filters.priority}
          onChange={(e) => setFilters({ ...filters, priority: e.target.value })}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Priorities</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>
        
        <div className="flex items-center space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={filters.due_soon}
              onChange={(e) => setFilters({ ...filters, due_soon: e.target.checked })}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">Due Soon</span>
          </label>
          
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={filters.overdue}
              onChange={(e) => setFilters({ ...filters, overdue: e.target.checked })}
              className="rounded border-gray-300 text-red-600 focus:ring-red-500"
            />
            <span className="text-sm text-gray-700">Overdue</span>
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Learning Goals</h1>
          <p className="text-gray-600">
            Set, track, and achieve your medical education objectives
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>New Goal</span>
        </button>
      </div>

      {renderAnalyticsCards()}

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'active', label: 'Active Goals', count: analytics?.active_goals },
            { id: 'completed', label: 'Completed', count: analytics?.completed_goals },
            { id: 'all', label: 'All Goals', count: analytics?.total_goals },
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span>{tab.label}</span>
              {tab.count !== undefined && (
                <span className={`px-2 py-0.5 rounded-full text-xs ${
                  activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {renderFilters()}

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {goals.map(goal => renderGoalCard(goal))}
        </div>
      )}

      {goals.length === 0 && !loading && (
        <div className="text-center py-12">
          <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No goals found</h3>
          <p className="text-gray-500 mb-4">
            {activeTab === 'active' 
              ? "You don't have any active goals. Create your first goal to get started!"
              : "No goals match your current filters."
            }
          </p>
          {activeTab === 'active' && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Your First Goal
            </button>
          )}
        </div>
      )}
    </div>
  );
};
