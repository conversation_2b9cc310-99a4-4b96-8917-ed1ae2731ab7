import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateLearningPathsAndGoals1735567200000 implements MigrationInterface {
  name = 'CreateLearningPathsAndGoals1735567200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create learning_paths table
    await queryRunner.query(`
      CREATE TABLE "learning_paths" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "title" character varying(255) NOT NULL,
        "description" text NOT NULL,
        "type" character varying NOT NULL DEFAULT 'template',
        "category" character varying NOT NULL DEFAULT 'custom',
        "difficulty" character varying NOT NULL DEFAULT 'intermediate',
        "status" character varying NOT NULL DEFAULT 'draft',
        "estimated_duration_weeks" integer NOT NULL DEFAULT '0',
        "estimated_hours_per_week" integer NOT NULL DEFAULT '0',
        "tags" text,
        "learning_objectives" text,
        "prerequisites" jsonb,
        "path_structure" jsonb NOT NULL,
        "analytics" jsonb,
        "created_by_id" uuid,
        "template_id" uuid,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_learning_paths" PRIMARY KEY ("id")
      )
    `);

    // Create learning_path_progress table
    await queryRunner.query(`
      CREATE TABLE "learning_path_progress" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "learning_path_id" uuid NOT NULL,
        "status" character varying NOT NULL DEFAULT 'not_started',
        "overall_progress_percentage" numeric(5,2) NOT NULL DEFAULT '0',
        "current_phase_index" integer NOT NULL DEFAULT '0',
        "current_module_index" integer NOT NULL DEFAULT '0',
        "phase_progress" jsonb,
        "module_progress" jsonb,
        "milestones_achieved" jsonb,
        "total_time_spent_minutes" integer NOT NULL DEFAULT '0',
        "started_at" TIMESTAMP,
        "completed_at" TIMESTAMP,
        "last_accessed_at" TIMESTAMP,
        "streak_days" integer NOT NULL DEFAULT '0',
        "last_activity_date" TIMESTAMP,
        "preferences" jsonb,
        "analytics" jsonb,
        "notes" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_learning_path_progress" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_learning_path_progress_user_path" UNIQUE ("user_id", "learning_path_id")
      )
    `);

    // Create learning_path_milestones table
    await queryRunner.query(`
      CREATE TABLE "learning_path_milestones" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "learning_path_id" uuid NOT NULL,
        "title" character varying(255) NOT NULL,
        "description" text NOT NULL,
        "type" character varying NOT NULL DEFAULT 'custom',
        "status" character varying NOT NULL DEFAULT 'active',
        "order" integer NOT NULL DEFAULT '0',
        "criteria" jsonb NOT NULL,
        "rewards" jsonb,
        "icon_url" character varying(500),
        "color" character varying(50),
        "is_required" boolean NOT NULL DEFAULT true,
        "is_visible" boolean NOT NULL DEFAULT true,
        "celebration_config" jsonb,
        "analytics" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_learning_path_milestones" PRIMARY KEY ("id")
      )
    `);

    // Create learning_goals table
    await queryRunner.query(`
      CREATE TABLE "learning_goals" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "user_id" uuid NOT NULL,
        "title" character varying(255) NOT NULL,
        "description" text NOT NULL,
        "type" character varying NOT NULL DEFAULT 'custom',
        "category" character varying NOT NULL DEFAULT 'custom',
        "status" character varying NOT NULL DEFAULT 'active',
        "priority" character varying NOT NULL DEFAULT 'medium',
        "target_criteria" jsonb NOT NULL,
        "start_date" TIMESTAMP NOT NULL,
        "target_date" TIMESTAMP NOT NULL,
        "completed_at" TIMESTAMP,
        "learning_path_id" uuid,
        "course_id" uuid,
        "related_resources" text,
        "smart_criteria" jsonb,
        "milestones" jsonb,
        "reminder_settings" jsonb,
        "progress_tracking" jsonb,
        "progress_percentage" numeric(5,2) NOT NULL DEFAULT '0',
        "streak_count" integer NOT NULL DEFAULT '0',
        "last_progress_update" TIMESTAMP,
        "analytics" jsonb,
        "notes" text,
        "tags" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_learning_goals" PRIMARY KEY ("id")
      )
    `);

    // Create learning_goal_progress table
    await queryRunner.query(`
      CREATE TABLE "learning_goal_progress" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "learning_goal_id" uuid NOT NULL,
        "user_id" uuid NOT NULL,
        "entry_type" character varying NOT NULL DEFAULT 'manual',
        "progress_value" numeric(10,2) NOT NULL,
        "progress_percentage" numeric(5,2) NOT NULL,
        "previous_value" numeric(10,2),
        "delta_value" numeric(10,2) NOT NULL,
        "unit" character varying(100),
        "notes" text,
        "metadata" jsonb,
        "context" jsonb,
        "recorded_at" TIMESTAMP NOT NULL,
        "is_milestone" boolean NOT NULL DEFAULT false,
        "is_streak_contribution" boolean NOT NULL DEFAULT false,
        "celebration_data" jsonb,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_learning_goal_progress" PRIMARY KEY ("id")
      )
    `);

    // Create junction tables for many-to-many relationships
    await queryRunner.query(`
      CREATE TABLE "learning_path_courses" (
        "learning_path_id" uuid NOT NULL,
        "course_id" uuid NOT NULL,
        CONSTRAINT "PK_learning_path_courses" PRIMARY KEY ("learning_path_id", "course_id")
      )
    `);

    await queryRunner.query(`
      CREATE TABLE "learning_path_assessments" (
        "learning_path_id" uuid NOT NULL,
        "assessment_id" uuid NOT NULL,
        CONSTRAINT "PK_learning_path_assessments" PRIMARY KEY ("learning_path_id", "assessment_id")
      )
    `);

    await queryRunner.query(`
      CREATE TABLE "learning_path_clinical_cases" (
        "learning_path_id" uuid NOT NULL,
        "clinical_case_id" uuid NOT NULL,
        CONSTRAINT "PK_learning_path_clinical_cases" PRIMARY KEY ("learning_path_id", "clinical_case_id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "learning_paths" 
      ADD CONSTRAINT "FK_learning_paths_created_by" 
      FOREIGN KEY ("created_by_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_paths" 
      ADD CONSTRAINT "FK_learning_paths_template" 
      FOREIGN KEY ("template_id") REFERENCES "learning_paths"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_path_progress" 
      ADD CONSTRAINT "FK_learning_path_progress_user" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_path_progress" 
      ADD CONSTRAINT "FK_learning_path_progress_path" 
      FOREIGN KEY ("learning_path_id") REFERENCES "learning_paths"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_path_milestones" 
      ADD CONSTRAINT "FK_learning_path_milestones_path" 
      FOREIGN KEY ("learning_path_id") REFERENCES "learning_paths"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_goals" 
      ADD CONSTRAINT "FK_learning_goals_user" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_goals" 
      ADD CONSTRAINT "FK_learning_goals_learning_path" 
      FOREIGN KEY ("learning_path_id") REFERENCES "learning_paths"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_goals" 
      ADD CONSTRAINT "FK_learning_goals_course" 
      FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_goal_progress" 
      ADD CONSTRAINT "FK_learning_goal_progress_goal" 
      FOREIGN KEY ("learning_goal_id") REFERENCES "learning_goals"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_goal_progress" 
      ADD CONSTRAINT "FK_learning_goal_progress_user" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Add foreign keys for junction tables
    await queryRunner.query(`
      ALTER TABLE "learning_path_courses" 
      ADD CONSTRAINT "FK_learning_path_courses_path" 
      FOREIGN KEY ("learning_path_id") REFERENCES "learning_paths"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_path_courses" 
      ADD CONSTRAINT "FK_learning_path_courses_course" 
      FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_path_assessments" 
      ADD CONSTRAINT "FK_learning_path_assessments_path" 
      FOREIGN KEY ("learning_path_id") REFERENCES "learning_paths"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_path_assessments" 
      ADD CONSTRAINT "FK_learning_path_assessments_assessment" 
      FOREIGN KEY ("assessment_id") REFERENCES "assessments"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_path_clinical_cases" 
      ADD CONSTRAINT "FK_learning_path_clinical_cases_path" 
      FOREIGN KEY ("learning_path_id") REFERENCES "learning_paths"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);

    await queryRunner.query(`
      ALTER TABLE "learning_path_clinical_cases" 
      ADD CONSTRAINT "FK_learning_path_clinical_cases_case" 
      FOREIGN KEY ("clinical_case_id") REFERENCES "clinical_cases"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_learning_paths_created_by" ON "learning_paths" ("created_by_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_paths_template" ON "learning_paths" ("template_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_paths_category" ON "learning_paths" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_paths_status" ON "learning_paths" ("status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_learning_path_progress_user" ON "learning_path_progress" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_path_progress_path" ON "learning_path_progress" ("learning_path_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_path_progress_status" ON "learning_path_progress" ("status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_learning_path_milestones_path" ON "learning_path_milestones" ("learning_path_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_path_milestones_type" ON "learning_path_milestones" ("type")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_learning_goals_user" ON "learning_goals" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_goals_status" ON "learning_goals" ("status")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_goals_category" ON "learning_goals" ("category")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_goals_target_date" ON "learning_goals" ("target_date")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_learning_goal_progress_goal" ON "learning_goal_progress" ("learning_goal_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_goal_progress_user" ON "learning_goal_progress" ("user_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_learning_goal_progress_recorded_at" ON "learning_goal_progress" ("recorded_at")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_learning_goal_progress_recorded_at"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_goal_progress_user"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_goal_progress_goal"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_goals_target_date"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_goals_category"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_goals_status"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_goals_user"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_path_milestones_type"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_path_milestones_path"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_path_progress_status"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_path_progress_path"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_path_progress_user"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_paths_status"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_paths_category"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_paths_template"`);
    await queryRunner.query(`DROP INDEX "IDX_learning_paths_created_by"`);

    // Drop junction tables
    await queryRunner.query(`DROP TABLE "learning_path_clinical_cases"`);
    await queryRunner.query(`DROP TABLE "learning_path_assessments"`);
    await queryRunner.query(`DROP TABLE "learning_path_courses"`);

    // Drop main tables
    await queryRunner.query(`DROP TABLE "learning_goal_progress"`);
    await queryRunner.query(`DROP TABLE "learning_goals"`);
    await queryRunner.query(`DROP TABLE "learning_path_milestones"`);
    await queryRunner.query(`DROP TABLE "learning_path_progress"`);
    await queryRunner.query(`DROP TABLE "learning_paths"`);
  }
}
