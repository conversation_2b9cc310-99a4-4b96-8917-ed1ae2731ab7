#!/usr/bin/env pwsh
# Test Docker builds for all services

Write-Host "Testing Docker builds for MedTrack Hub..." -ForegroundColor Green

# Function to test a Docker build
function Test-DockerBuild {
    param(
        [string]$ServiceName,
        [string]$DockerfilePath,
        [string]$Context
    )
    
    Write-Host "`nTesting $ServiceName build..." -ForegroundColor Yellow
    
    try {
        $buildResult = docker build -t "medtrack-$ServiceName-test" -f $DockerfilePath $Context 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $ServiceName build successful" -ForegroundColor Green
            # Clean up test image
            docker rmi "medtrack-$ServiceName-test" -f | Out-Null
            return $true
        } else {
            Write-Host "❌ $ServiceName build failed" -ForegroundColor Red
            Write-Host $buildResult -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ $ServiceName build error: $_" -ForegroundColor Red
        return $false
    }
}

# Test Docker daemon
Write-Host "Checking Docker daemon..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Docker daemon not running. Please start Docker Desktop." -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Docker daemon is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker not available: $_" -ForegroundColor Red
    exit 1
}

# Test builds
$results = @{}

# Test Frontend (npm)
$results["Frontend"] = Test-DockerBuild -ServiceName "frontend" -DockerfilePath "./frontend/Dockerfile" -Context "./frontend"

# Test Backend (pnpm)
$results["Backend"] = Test-DockerBuild -ServiceName "backend" -DockerfilePath "./backend/Dockerfile" -Context "./backend"

# Test Python Analytics
$results["Analytics"] = Test-DockerBuild -ServiceName "analytics" -DockerfilePath "./backend/python_analytics/Dockerfile" -Context "./backend/python_analytics"

# Summary
Write-Host "`n=== BUILD TEST SUMMARY ===" -ForegroundColor Cyan
$allPassed = $true
foreach ($service in $results.Keys) {
    $status = if ($results[$service]) { "✅ PASS" } else { "❌ FAIL"; $allPassed = $false }
    Write-Host "$service`: $status" -ForegroundColor $(if ($results[$service]) { "Green" } else { "Red" })
}

if ($allPassed) {
    Write-Host "`n🎉 All Docker builds passed!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n💥 Some Docker builds failed. Check the errors above." -ForegroundColor Red
    exit 1
}
