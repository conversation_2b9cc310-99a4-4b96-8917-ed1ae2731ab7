'use client';

import React from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { LearningPathVisualization } from '@/components/learning-paths/LearningPathVisualization';

interface LearningPathDetailPageProps {
  params: {
    id: string;
  };
}

export default function LearningPathDetailPage({ params }: LearningPathDetailPageProps) {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        <LearningPathVisualization pathId={params.id} />
      </div>
    </ProtectedRoute>
  );
}
