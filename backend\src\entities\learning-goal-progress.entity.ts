import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { LearningGoal } from './learning-goal.entity';

export enum ProgressEntryType {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
  MILESTONE = 'milestone',
  ADJUSTMENT = 'adjustment',
}

@Entity('learning_goal_progress')
export class LearningGoalProgress {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  learning_goal_id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({
    type: 'enum',
    enum: ProgressEntryType,
    default: ProgressEntryType.MANUAL,
  })
  entry_type: ProgressEntryType;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  progress_value: number;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  progress_percentage: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  previous_value: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  delta_value: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  unit: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    source?: string;
    session_id?: string;
    activity_type?: string;
    duration_minutes?: number;
    score?: number;
    milestone_id?: string;
    auto_generated?: boolean;
  };

  @Column({ type: 'jsonb', nullable: true })
  context: {
    course_id?: string;
    assessment_id?: string;
    clinical_case_id?: string;
    learning_path_phase?: string;
    study_session_id?: string;
  };

  @Column({ type: 'timestamp' })
  recorded_at: Date;

  @Column({ type: 'boolean', default: false })
  is_milestone: boolean;

  @Column({ type: 'boolean', default: false })
  is_streak_contribution: boolean;

  @Column({ type: 'jsonb', nullable: true })
  celebration_data: {
    triggered: boolean;
    type: 'milestone' | 'streak' | 'completion' | 'improvement';
    message: string;
    points_awarded?: number;
    badge_earned?: string;
  };

  @ManyToOne(() => LearningGoal, (goal) => goal.progress_entries, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'learning_goal_id' })
  learning_goal: LearningGoal;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
