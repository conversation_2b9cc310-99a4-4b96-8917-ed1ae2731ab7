import { DataSource } from 'typeorm';
import 'dotenv/config';
import { join } from 'path';

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  username: process.env.POSTGRES_USER || 'medical',
  password: process.env.POSTGRES_PASSWORD || 'AU110s/6081/2021MT',
  database: process.env.POSTGRES_DB || 'medical_tracker',
  entities: [join(__dirname, '..', '**', '*.entity.{ts,js}')],
  migrations: [
    join(__dirname, '..', 'database', 'migrations', '*{.ts,.js}'),
    join(__dirname, '..', 'migrations', '*{.ts,.js}'),
  ],
  migrationsTableName: 'migrations',
  synchronize: false,
  logging: process.env.NODE_ENV !== 'production',
});

export default AppDataSource;
