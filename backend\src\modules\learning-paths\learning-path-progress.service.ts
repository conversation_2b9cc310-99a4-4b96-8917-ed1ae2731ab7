import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LearningPathProgress, LearningPathProgressStatus } from '../../entities/learning-path-progress.entity';
import { LearningPath } from '../../entities/learning-path.entity';
import { LearningPathMilestone } from '../../entities/learning-path-milestone.entity';
import { User } from '../../entities/user.entity';

export interface StartLearningPathDto {
  learning_path_id: string;
  preferences?: {
    daily_study_goal_minutes?: number;
    preferred_study_times?: string[];
    reminder_settings?: any;
    difficulty_preference?: 'adaptive' | 'fixed';
  };
}

export interface UpdateProgressDto {
  phase_id?: string;
  module_id?: string;
  progress_percentage?: number;
  time_spent_minutes?: number;
  notes?: string;
  module_status?: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  score?: number;
}

export interface ProgressAnalytics {
  overall_progress: number;
  time_spent_total: number;
  current_streak: number;
  modules_completed: number;
  modules_total: number;
  phases_completed: number;
  phases_total: number;
  milestones_achieved: number;
  milestones_total: number;
  estimated_completion_date?: Date;
  learning_velocity: {
    modules_per_week: number;
    hours_per_week: number;
    consistency_score: number;
  };
}

@Injectable()
export class LearningPathProgressService {
  constructor(
    @InjectRepository(LearningPathProgress)
    private progressRepository: Repository<LearningPathProgress>,
    @InjectRepository(LearningPath)
    private learningPathRepository: Repository<LearningPath>,
    @InjectRepository(LearningPathMilestone)
    private milestoneRepository: Repository<LearningPathMilestone>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async startLearningPath(userId: string, startDto: StartLearningPathDto): Promise<LearningPathProgress> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const learningPath = await this.learningPathRepository.findOne({
      where: { id: startDto.learning_path_id },
      relations: ['milestones'],
    });
    if (!learningPath) {
      throw new NotFoundException('Learning path not found');
    }

    // Check if user already has progress for this path
    const existingProgress = await this.progressRepository.findOne({
      where: { user_id: userId, learning_path_id: startDto.learning_path_id },
    });

    if (existingProgress) {
      throw new BadRequestException('User is already enrolled in this learning path');
    }

    // Initialize progress structure based on path structure
    const phaseProgress = learningPath.path_structure.phases.map(phase => ({
      phase_id: phase.id,
      status: 'not_started' as const,
      progress_percentage: 0,
      modules_completed: [],
      current_module_id: phase.modules[0]?.id,
    }));

    const moduleProgress = learningPath.path_structure.phases.flatMap(phase =>
      phase.modules.map(module => ({
        module_id: module.id,
        phase_id: phase.id,
        status: 'not_started' as const,
        progress_percentage: 0,
        time_spent_minutes: 0,
        attempts: 0,
      }))
    );

    const progress = this.progressRepository.create({
      user_id: userId,
      learning_path_id: startDto.learning_path_id,
      status: LearningPathProgressStatus.IN_PROGRESS,
      overall_progress_percentage: 0,
      current_phase_index: 0,
      current_module_index: 0,
      phase_progress: phaseProgress,
      module_progress: moduleProgress,
      milestones_achieved: [],
      total_time_spent_minutes: 0,
      started_at: new Date(),
      last_accessed_at: new Date(),
      streak_days: 0,
      last_activity_date: new Date(),
      preferences: startDto.preferences || {},
      analytics: {
        study_sessions: [],
        performance_trends: [],
        learning_velocity: {
          modules_per_week: 0,
          hours_per_week: 0,
          consistency_score: 0,
        },
      },
    });

    const savedProgress = await this.progressRepository.save(progress);

    // Update learning path analytics
    await this.updateLearningPathEnrollmentCount(startDto.learning_path_id);

    return savedProgress;
  }

  async updateProgress(userId: string, learningPathId: string, updateDto: UpdateProgressDto): Promise<LearningPathProgress> {
    const progress = await this.progressRepository.findOne({
      where: { user_id: userId, learning_path_id: learningPathId },
      relations: ['learning_path'],
    });

    if (!progress) {
      throw new NotFoundException('Learning path progress not found');
    }

    const now = new Date();
    let progressUpdated = false;

    // Update module progress if module_id is provided
    if (updateDto.module_id) {
      const moduleIndex = progress.module_progress.findIndex(m => m.module_id === updateDto.module_id);
      if (moduleIndex !== -1) {
        const moduleProgress = progress.module_progress[moduleIndex];
        
        if (updateDto.module_status) {
          moduleProgress.status = updateDto.module_status;
        }
        
        if (updateDto.progress_percentage !== undefined) {
          moduleProgress.progress_percentage = updateDto.progress_percentage;
        }
        
        if (updateDto.time_spent_minutes) {
          moduleProgress.time_spent_minutes += updateDto.time_spent_minutes;
          progress.total_time_spent_minutes += updateDto.time_spent_minutes;
        }
        
        if (updateDto.score !== undefined) {
          moduleProgress.best_score = Math.max(moduleProgress.best_score || 0, updateDto.score);
        }
        
        if (updateDto.notes) {
          moduleProgress.notes = updateDto.notes;
        }

        moduleProgress.attempts += 1;
        progressUpdated = true;

        // Update phase progress
        await this.updatePhaseProgress(progress, updateDto.phase_id || this.getPhaseIdForModule(progress, updateDto.module_id));
      }
    }

    // Update overall progress
    if (progressUpdated) {
      progress.overall_progress_percentage = this.calculateOverallProgress(progress);
      progress.last_accessed_at = now;
      
      // Update streak
      await this.updateStreak(progress);
      
      // Check for milestone achievements
      await this.checkMilestoneAchievements(progress);
      
      // Add study session to analytics
      this.addStudySession(progress, updateDto.time_spent_minutes || 0, updateDto.module_id);
    }

    return await this.progressRepository.save(progress);
  }

  async getProgress(userId: string, learningPathId: string): Promise<LearningPathProgress> {
    const progress = await this.progressRepository.findOne({
      where: { user_id: userId, learning_path_id: learningPathId },
      relations: ['learning_path', 'learning_path.milestones'],
    });

    if (!progress) {
      throw new NotFoundException('Learning path progress not found');
    }

    return progress;
  }

  async getUserProgress(userId: string): Promise<LearningPathProgress[]> {
    return await this.progressRepository.find({
      where: { user_id: userId },
      relations: ['learning_path', 'learning_path.created_by'],
      order: { last_accessed_at: 'DESC' },
    });
  }

  async getProgressAnalytics(userId: string, learningPathId: string): Promise<ProgressAnalytics> {
    const progress = await this.getProgress(userId, learningPathId);
    const learningPath = progress.learning_path;

    const totalModules = progress.module_progress.length;
    const completedModules = progress.module_progress.filter(m => m.status === 'completed').length;
    
    const totalPhases = progress.phase_progress.length;
    const completedPhases = progress.phase_progress.filter(p => p.status === 'completed').length;
    
    const totalMilestones = learningPath.milestones?.length || 0;
    const achievedMilestones = progress.milestones_achieved.length;

    // Calculate estimated completion date
    const estimatedCompletionDate = this.calculateEstimatedCompletion(progress);

    return {
      overall_progress: progress.overall_progress_percentage,
      time_spent_total: progress.total_time_spent_minutes,
      current_streak: progress.streak_days,
      modules_completed: completedModules,
      modules_total: totalModules,
      phases_completed: completedPhases,
      phases_total: totalPhases,
      milestones_achieved: achievedMilestones,
      milestones_total: totalMilestones,
      estimated_completion_date: estimatedCompletionDate,
      learning_velocity: progress.analytics.learning_velocity,
    };
  }

  async pauseLearningPath(userId: string, learningPathId: string): Promise<LearningPathProgress> {
    const progress = await this.getProgress(userId, learningPathId);
    progress.status = LearningPathProgressStatus.PAUSED;
    return await this.progressRepository.save(progress);
  }

  async resumeLearningPath(userId: string, learningPathId: string): Promise<LearningPathProgress> {
    const progress = await this.getProgress(userId, learningPathId);
    progress.status = LearningPathProgressStatus.IN_PROGRESS;
    progress.last_accessed_at = new Date();
    return await this.progressRepository.save(progress);
  }

  async completeLearningPath(userId: string, learningPathId: string): Promise<LearningPathProgress> {
    const progress = await this.getProgress(userId, learningPathId);
    progress.status = LearningPathProgressStatus.COMPLETED;
    progress.completed_at = new Date();
    progress.overall_progress_percentage = 100;
    
    // Update learning path completion analytics
    await this.updateLearningPathCompletionStats(learningPathId, progress);
    
    return await this.progressRepository.save(progress);
  }

  private async updatePhaseProgress(progress: LearningPathProgress, phaseId: string): Promise<void> {
    const phaseIndex = progress.phase_progress.findIndex(p => p.phase_id === phaseId);
    if (phaseIndex === -1) return;

    const phaseProgress = progress.phase_progress[phaseIndex];
    const phaseModules = progress.module_progress.filter(m => m.phase_id === phaseId);
    
    const completedModules = phaseModules.filter(m => m.status === 'completed');
    const totalModules = phaseModules.length;
    
    phaseProgress.progress_percentage = totalModules > 0 ? (completedModules.length / totalModules) * 100 : 0;
    phaseProgress.modules_completed = completedModules.map(m => m.module_id);
    
    if (phaseProgress.progress_percentage === 100 && phaseProgress.status !== 'completed') {
      phaseProgress.status = 'completed';
      phaseProgress.completed_at = new Date();
    } else if (phaseProgress.progress_percentage > 0 && phaseProgress.status === 'not_started') {
      phaseProgress.status = 'in_progress';
      phaseProgress.started_at = new Date();
    }
  }

  private calculateOverallProgress(progress: LearningPathProgress): number {
    const totalModules = progress.module_progress.length;
    if (totalModules === 0) return 0;

    const totalProgress = progress.module_progress.reduce((sum, module) => sum + module.progress_percentage, 0);
    return Math.round(totalProgress / totalModules);
  }

  private async updateStreak(progress: LearningPathProgress): Promise<void> {
    const today = new Date();
    const lastActivity = progress.last_activity_date;
    
    if (!lastActivity) {
      progress.streak_days = 1;
      progress.last_activity_date = today;
      return;
    }

    const daysDiff = Math.floor((today.getTime() - lastActivity.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === 1) {
      progress.streak_days += 1;
    } else if (daysDiff > 1) {
      progress.streak_days = 1;
    }
    
    progress.last_activity_date = today;
  }

  private async checkMilestoneAchievements(progress: LearningPathProgress): Promise<void> {
    const milestones = await this.milestoneRepository.find({
      where: { learning_path_id: progress.learning_path_id, status: 'active' },
    });

    for (const milestone of milestones) {
      const alreadyAchieved = progress.milestones_achieved.some(m => m.milestone_id === milestone.id);
      if (alreadyAchieved) continue;

      const isAchieved = this.evaluateMilestoneCriteria(milestone, progress);
      if (isAchieved) {
        progress.milestones_achieved.push({
          milestone_id: milestone.id,
          achieved_at: new Date(),
          notes: `Milestone achieved: ${milestone.title}`,
        });
      }
    }
  }

  private evaluateMilestoneCriteria(milestone: LearningPathMilestone, progress: LearningPathProgress): boolean {
    const criteria = milestone.criteria;
    
    switch (criteria.type) {
      case 'phase_completion':
        return progress.phase_progress.some(p => 
          p.phase_id === criteria.conditions.phase_id && p.status === 'completed'
        );
      
      case 'time_based':
        return progress.total_time_spent_minutes >= (criteria.conditions.time_period_days || 0) * 60;
      
      case 'streak':
        return progress.streak_days >= (criteria.conditions.streak_days || 0);
      
      default:
        return false;
    }
  }

  private addStudySession(progress: LearningPathProgress, duration: number, moduleId?: string): void {
    const session = {
      date: new Date(),
      duration_minutes: duration,
      modules_covered: moduleId ? [moduleId] : [],
    };

    progress.analytics.study_sessions.push(session);
    
    // Keep only last 100 sessions
    if (progress.analytics.study_sessions.length > 100) {
      progress.analytics.study_sessions = progress.analytics.study_sessions.slice(-100);
    }
  }

  private calculateEstimatedCompletion(progress: LearningPathProgress): Date | undefined {
    const remainingProgress = 100 - progress.overall_progress_percentage;
    if (remainingProgress <= 0) return undefined;

    const recentSessions = progress.analytics.study_sessions.slice(-10);
    if (recentSessions.length === 0) return undefined;

    const avgSessionDuration = recentSessions.reduce((sum, s) => sum + s.duration_minutes, 0) / recentSessions.length;
    const estimatedRemainingHours = (remainingProgress / 100) * (progress.learning_path.estimated_duration_weeks * progress.learning_path.estimated_hours_per_week);
    const estimatedRemainingMinutes = estimatedRemainingHours * 60;
    const estimatedSessionsNeeded = Math.ceil(estimatedRemainingMinutes / avgSessionDuration);
    
    // Assume 3 sessions per week
    const weeksNeeded = Math.ceil(estimatedSessionsNeeded / 3);
    const estimatedDate = new Date();
    estimatedDate.setDate(estimatedDate.getDate() + (weeksNeeded * 7));
    
    return estimatedDate;
  }

  private getPhaseIdForModule(progress: LearningPathProgress, moduleId: string): string {
    const moduleProgress = progress.module_progress.find(m => m.module_id === moduleId);
    return moduleProgress?.phase_id || '';
  }

  private async updateLearningPathEnrollmentCount(learningPathId: string): Promise<void> {
    const enrollmentCount = await this.progressRepository.count({
      where: { learning_path_id: learningPathId },
    });

    await this.learningPathRepository.update(learningPathId, {
      analytics: () => `jsonb_set(analytics, '{total_enrollments}', '${enrollmentCount}')`,
    });
  }

  private async updateLearningPathCompletionStats(learningPathId: string, completedProgress: LearningPathProgress): Promise<void> {
    const totalEnrollments = await this.progressRepository.count({
      where: { learning_path_id: learningPathId },
    });

    const completions = await this.progressRepository.count({
      where: { learning_path_id: learningPathId, status: LearningPathProgressStatus.COMPLETED },
    });

    const completionRate = totalEnrollments > 0 ? (completions / totalEnrollments) * 100 : 0;

    // Calculate average completion time
    const completedProgresses = await this.progressRepository.find({
      where: { learning_path_id: learningPathId, status: LearningPathProgressStatus.COMPLETED },
    });

    let avgCompletionTime = 0;
    if (completedProgresses.length > 0) {
      const totalCompletionTime = completedProgresses.reduce((sum, p) => {
        if (p.started_at && p.completed_at) {
          const weeks = (p.completed_at.getTime() - p.started_at.getTime()) / (1000 * 60 * 60 * 24 * 7);
          return sum + weeks;
        }
        return sum;
      }, 0);
      avgCompletionTime = totalCompletionTime / completedProgresses.length;
    }

    await this.learningPathRepository.update(learningPathId, {
      analytics: () => `jsonb_set(jsonb_set(analytics, '{completion_rate}', '${completionRate}'), '{average_completion_time_weeks}', '${avgCompletionTime}')`,
    });
  }
}
