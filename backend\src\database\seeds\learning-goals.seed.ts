import { DataSource } from 'typeorm';
import { LearningGoal, GoalType, GoalCategory, GoalStatus, GoalPriority } from '../../entities/learning-goal.entity';
import { User } from '../../entities/user.entity';
import { LearningPath } from '../../entities/learning-path.entity';

export async function seedLearningGoals(dataSource: DataSource) {
  const goalRepository = dataSource.getRepository(LearningGoal);
  const userRepository = dataSource.getRepository(User);
  const pathRepository = dataSource.getRepository(LearningPath);

  // Find existing users (assuming some test users exist)
  const users = await userRepository.find({ take: 3 });
  if (users.length === 0) {
    console.log('No users found for seeding goals');
    return;
  }

  // Find existing learning paths
  const paths = await pathRepository.find({ take: 3 });

  // Sample goals for different users
  const sampleGoals = [
    // USMLE Preparation Goals
    {
      title: 'Achieve USMLE Step 1 Target Score',
      description: 'Score 240+ on USMLE Step 1 examination to be competitive for internal medicine residency programs.',
      type: GoalType.CUSTOM,
      category: GoalCategory.ASSESSMENT_SCORE,
      priority: GoalPriority.CRITICAL,
      target_criteria: {
        type: 'score' as const,
        target_value: 240,
        current_value: 0,
        unit: 'points',
        measurement_method: 'USMLE Step 1 score',
      },
      start_date: new Date(),
      target_date: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000), // 120 days from now
      smart_criteria: {
        specific: 'Achieve a score of 240 or higher on the USMLE Step 1 examination',
        measurable: 'Score will be measured by official USMLE Step 1 results',
        achievable: 'Based on practice test scores and dedicated study plan',
        relevant: 'Essential for competitive residency applications in internal medicine',
        time_bound: 'Complete within 4 months of dedicated study period',
      },
      milestones: [
        {
          id: 'milestone_1',
          title: 'First Practice Test Baseline',
          description: 'Complete first full-length practice test to establish baseline',
          target_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          target_value: 200,
          order: 1,
          completed: false,
        },
        {
          id: 'milestone_2',
          title: 'Mid-Study Assessment',
          description: 'Achieve 220+ on practice test at midpoint',
          target_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
          target_value: 220,
          order: 2,
          completed: false,
        },
        {
          id: 'milestone_3',
          title: 'Final Practice Test',
          description: 'Score 235+ on final practice test before real exam',
          target_date: new Date(Date.now() + 110 * 24 * 60 * 60 * 1000),
          target_value: 235,
          order: 3,
          completed: false,
        },
      ],
      reminder_settings: {
        enabled: true,
        frequency: 'daily',
        time: '08:00',
      },
      progress_tracking: {
        auto_tracking: true,
        manual_updates: true,
        data_sources: ['practice_tests', 'qbank_scores'],
        update_frequency: 'weekly',
      },
    },
    
    // Study Time Goals
    {
      title: 'Daily Study Consistency',
      description: 'Maintain consistent daily study schedule of 6+ hours for USMLE preparation.',
      type: GoalType.DAILY,
      category: GoalCategory.STUDY_TIME,
      priority: GoalPriority.HIGH,
      target_criteria: {
        type: 'numeric' as const,
        target_value: 180, // 6 hours * 30 days
        current_value: 0,
        unit: 'hours',
        measurement_method: 'Daily study time tracking',
      },
      start_date: new Date(),
      target_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      smart_criteria: {
        specific: 'Study for at least 6 hours every day for medical board preparation',
        measurable: 'Track daily study hours using time tracking app',
        achievable: 'Realistic based on current schedule and commitments',
        relevant: 'Consistent study time is crucial for USMLE success',
        time_bound: 'Maintain for 30 consecutive days',
      },
      milestones: [
        {
          id: 'milestone_1',
          title: 'First Week Consistency',
          description: 'Complete 7 consecutive days of 6+ hour study sessions',
          target_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          target_value: 42,
          order: 1,
          completed: false,
        },
        {
          id: 'milestone_2',
          title: 'Two Week Streak',
          description: 'Maintain study consistency for 14 days',
          target_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          target_value: 84,
          order: 2,
          completed: false,
        },
      ],
      reminder_settings: {
        enabled: true,
        frequency: 'daily',
        time: '07:00',
      },
      progress_tracking: {
        auto_tracking: false,
        manual_updates: true,
        data_sources: ['manual_entry'],
        update_frequency: 'daily',
      },
    },

    // Clinical Skills Goal
    {
      title: 'Master Physical Examination Skills',
      description: 'Achieve proficiency in comprehensive physical examination techniques across all major organ systems.',
      type: GoalType.MONTHLY,
      category: GoalCategory.SKILL_MASTERY,
      priority: GoalPriority.HIGH,
      target_criteria: {
        type: 'percentage' as const,
        target_value: 90,
        current_value: 0,
        unit: '%',
        measurement_method: 'Clinical skills assessment scores',
      },
      start_date: new Date(),
      target_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
      smart_criteria: {
        specific: 'Master physical examination techniques for cardiovascular, respiratory, abdominal, and neurological systems',
        measurable: 'Achieve 90% or higher on standardized clinical skills assessments',
        achievable: 'With dedicated practice and feedback from clinical instructors',
        relevant: 'Essential for clinical rotations and patient care competency',
        time_bound: 'Complete within 3 months of clinical skills training',
      },
      milestones: [
        {
          id: 'milestone_1',
          title: 'Cardiovascular Exam Mastery',
          description: 'Achieve 85%+ on cardiovascular examination assessment',
          target_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          target_value: 85,
          order: 1,
          completed: false,
        },
        {
          id: 'milestone_2',
          title: 'Respiratory Exam Proficiency',
          description: 'Demonstrate proficiency in respiratory examination',
          target_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
          target_value: 85,
          order: 2,
          completed: false,
        },
      ],
      reminder_settings: {
        enabled: true,
        frequency: 'weekly',
        time: '10:00',
      },
      progress_tracking: {
        auto_tracking: true,
        manual_updates: true,
        data_sources: ['clinical_assessments', 'skill_evaluations'],
        update_frequency: 'weekly',
      },
    },

    // Course Completion Goal
    {
      title: 'Complete Internal Medicine Rotation',
      description: 'Successfully complete internal medicine clinical rotation with honors grade.',
      type: GoalType.QUARTERLY,
      category: GoalCategory.COURSE_COMPLETION,
      priority: GoalPriority.CRITICAL,
      target_criteria: {
        type: 'completion' as const,
        target_value: 1,
        current_value: 0,
        unit: 'rotation',
        measurement_method: 'Clinical rotation completion with honors grade',
      },
      start_date: new Date(),
      target_date: new Date(Date.now() + 56 * 24 * 60 * 60 * 1000), // 8 weeks
      smart_criteria: {
        specific: 'Complete 8-week internal medicine clinical rotation with honors grade (90%+)',
        measurable: 'Final rotation grade and clinical evaluations',
        achievable: 'With dedicated preparation and active participation',
        relevant: 'Critical for internal medicine residency application',
        time_bound: 'Complete within the 8-week rotation period',
      },
      milestones: [
        {
          id: 'milestone_1',
          title: 'Mid-Rotation Evaluation',
          description: 'Receive satisfactory mid-rotation evaluation',
          target_date: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
          target_value: 85,
          order: 1,
          completed: false,
        },
        {
          id: 'milestone_2',
          title: 'Clinical Presentation Excellence',
          description: 'Deliver outstanding patient presentations',
          target_date: new Date(Date.now() + 42 * 24 * 60 * 60 * 1000),
          target_value: 90,
          order: 2,
          completed: false,
        },
      ],
      reminder_settings: {
        enabled: true,
        frequency: 'weekly',
        time: '18:00',
      },
      progress_tracking: {
        auto_tracking: true,
        manual_updates: true,
        data_sources: ['rotation_grades', 'clinical_evaluations'],
        update_frequency: 'weekly',
      },
    },

    // Research Goal
    {
      title: 'Complete Research Project',
      description: 'Complete and publish a research project in internal medicine to strengthen residency application.',
      type: GoalType.CUSTOM,
      category: GoalCategory.CUSTOM,
      priority: GoalPriority.MEDIUM,
      target_criteria: {
        type: 'completion' as const,
        target_value: 1,
        current_value: 0,
        unit: 'publication',
        measurement_method: 'Peer-reviewed publication or conference presentation',
      },
      start_date: new Date(),
      target_date: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 6 months
      smart_criteria: {
        specific: 'Complete original research project in internal medicine and submit for publication',
        measurable: 'Manuscript submission to peer-reviewed journal or conference presentation',
        achievable: 'With faculty mentorship and dedicated research time',
        relevant: 'Enhances competitiveness for internal medicine residency programs',
        time_bound: 'Complete within 6 months',
      },
      milestones: [
        {
          id: 'milestone_1',
          title: 'Research Proposal Approval',
          description: 'Get research proposal approved by faculty mentor',
          target_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          target_value: 1,
          order: 1,
          completed: false,
        },
        {
          id: 'milestone_2',
          title: 'Data Collection Complete',
          description: 'Complete all data collection for research project',
          target_date: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000),
          target_value: 1,
          order: 2,
          completed: false,
        },
        {
          id: 'milestone_3',
          title: 'Manuscript Draft',
          description: 'Complete first draft of research manuscript',
          target_date: new Date(Date.now() + 150 * 24 * 60 * 60 * 1000),
          target_value: 1,
          order: 3,
          completed: false,
        },
      ],
      reminder_settings: {
        enabled: true,
        frequency: 'weekly',
        time: '14:00',
      },
      progress_tracking: {
        auto_tracking: false,
        manual_updates: true,
        data_sources: ['manual_entry'],
        update_frequency: 'weekly',
      },
    },
  ];

  // Create goals for users
  for (let i = 0; i < Math.min(users.length, sampleGoals.length); i++) {
    const user = users[i];
    const goalData = sampleGoals[i];
    
    // Assign learning path if available
    const learningPath = paths[i % paths.length] || null;
    
    const goal = goalRepository.create(goalData);
    goal.user = user;
    goal.learning_path = learningPath;
    goal.progress_percentage = 0;
    goal.streak_count = 0;
    goal.analytics = {
      time_spent_minutes: 0,
      sessions_completed: 0,
      average_session_duration: 0,
      consistency_score: 0,
      velocity_trend: 'stable' as const,
    };

    await goalRepository.save(goal);
  }

  // Create additional goals for the first user to show variety
  if (users.length > 0) {
    const additionalGoals = [
      {
        title: 'Weekly Question Bank Practice',
        description: 'Complete 200+ practice questions weekly with 75%+ accuracy.',
        type: GoalType.WEEKLY,
        category: GoalCategory.ASSESSMENT_SCORE,
        priority: GoalPriority.HIGH,
        target_criteria: {
          type: 'numeric' as const,
          target_value: 800, // 200 questions * 4 weeks
          current_value: 0,
          unit: 'questions',
          measurement_method: 'Question bank completion tracking',
        },
        start_date: new Date(),
        target_date: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
        smart_criteria: {
          specific: 'Complete 200 practice questions each week with minimum 75% accuracy',
          measurable: 'Track questions completed and accuracy percentage',
          achievable: 'Approximately 30 questions per day',
          relevant: 'Essential for USMLE preparation and knowledge retention',
          time_bound: 'Maintain for 4 consecutive weeks',
        },
        user: users[0],
        progress_percentage: 0,
        streak_count: 0,
        analytics: {
          time_spent_minutes: 0,
          sessions_completed: 0,
          average_session_duration: 0,
          consistency_score: 0,
          velocity_trend: 'stable' as const,
        },
      },
      {
        title: 'Maintain Study Streak',
        description: 'Study every day for 30 consecutive days without missing a single day.',
        type: GoalType.MONTHLY,
        category: GoalCategory.STREAK_MAINTENANCE,
        priority: GoalPriority.MEDIUM,
        target_criteria: {
          type: 'numeric' as const,
          target_value: 30,
          current_value: 0,
          unit: 'days',
          measurement_method: 'Daily study session completion',
        },
        start_date: new Date(),
        target_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        smart_criteria: {
          specific: 'Study for at least 1 hour every day for 30 consecutive days',
          measurable: 'Track daily study sessions and streak count',
          achievable: 'Minimum 1 hour commitment is manageable',
          relevant: 'Builds consistent study habits and discipline',
          time_bound: '30-day streak challenge',
        },
        user: users[0],
        progress_percentage: 0,
        streak_count: 0,
        analytics: {
          time_spent_minutes: 0,
          sessions_completed: 0,
          average_session_duration: 0,
          consistency_score: 0,
          velocity_trend: 'stable' as const,
        },
      },
    ];

    for (const goalData of additionalGoals) {
      const goal = goalRepository.create(goalData);
      await goalRepository.save(goal);
    }
  }

  console.log('Learning goals seeded successfully!');
}
