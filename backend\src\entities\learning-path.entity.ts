import {
  Entity,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>any,
  JoinTable,
} from 'typeorm';
import { User } from './user.entity';
import { Course } from './course.entity';
import { Assessment } from './assessment.entity';
import { ClinicalCase } from './clinical-case.entity';
import { LearningPathProgress } from './learning-path-progress.entity';
import { LearningPathMilestone } from './learning-path-milestone.entity';

export enum LearningPathType {
  TEMPLATE = 'template',
  CUSTOM = 'custom',
  RECOMMENDED = 'recommended',
}

export enum LearningPathCategory {
  USMLE_STEP1 = 'usmle_step1',
  USMLE_STEP2 = 'usmle_step2',
  USMLE_STEP3 = 'usmle_step3',
  CLINICAL_SKILLS = 'clinical_skills',
  SPECIALTY_PREP = 'specialty_prep',
  RESEARCH_SKILLS = 'research_skills',
  BOARD_REVIEW = 'board_review',
  CONTINUING_EDUCATION = 'continuing_education',
  CUSTOM = 'custom',
}

export enum LearningPathDifficulty {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert',
}

export enum LearningPathStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived',
}

@Entity('learning_paths')
export class LearningPath {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: LearningPathType,
    default: LearningPathType.TEMPLATE,
  })
  type: LearningPathType;

  @Column({
    type: 'enum',
    enum: LearningPathCategory,
    default: LearningPathCategory.CUSTOM,
  })
  category: LearningPathCategory;

  @Column({
    type: 'enum',
    enum: LearningPathDifficulty,
    default: LearningPathDifficulty.INTERMEDIATE,
  })
  difficulty: LearningPathDifficulty;

  @Column({
    type: 'enum',
    enum: LearningPathStatus,
    default: LearningPathStatus.DRAFT,
  })
  status: LearningPathStatus;

  @Column({ type: 'int', default: 0 })
  estimated_duration_weeks: number;

  @Column({ type: 'int', default: 0 })
  estimated_hours_per_week: number;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @Column({ type: 'simple-array', nullable: true })
  learning_objectives: string[];

  @Column({ type: 'jsonb', nullable: true })
  prerequisites: {
    courses?: string[];
    assessments?: string[];
    skills?: string[];
    description?: string;
  };

  @Column({ type: 'jsonb' })
  path_structure: {
    phases: {
      id: string;
      title: string;
      description: string;
      order: number;
      estimated_duration_weeks: number;
      modules: {
        id: string;
        title: string;
        description: string;
        order: number;
        type: 'course' | 'assessment' | 'clinical_case' | 'milestone' | 'custom';
        resource_id?: string;
        estimated_duration_hours: number;
        is_required: boolean;
        unlock_conditions?: string[];
      }[];
    }[];
  };

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    total_enrollments: number;
    completion_rate: number;
    average_completion_time_weeks: number;
    user_ratings: {
      average: number;
      count: number;
    };
    difficulty_feedback: {
      too_easy: number;
      just_right: number;
      too_hard: number;
    };
  };

  @Column({ type: 'uuid', nullable: true })
  created_by_id: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'created_by_id' })
  created_by: User;

  @Column({ type: 'uuid', nullable: true })
  template_id: string;

  @ManyToOne(() => LearningPath, { nullable: true })
  @JoinColumn({ name: 'template_id' })
  template: LearningPath;

  @OneToMany(() => LearningPath, (path) => path.template)
  custom_instances: LearningPath[];

  @ManyToMany(() => Course)
  @JoinTable({
    name: 'learning_path_courses',
    joinColumn: { name: 'learning_path_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'course_id', referencedColumnName: 'id' },
  })
  courses: Course[];

  @ManyToMany(() => Assessment)
  @JoinTable({
    name: 'learning_path_assessments',
    joinColumn: { name: 'learning_path_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'assessment_id', referencedColumnName: 'id' },
  })
  assessments: Assessment[];

  @ManyToMany(() => ClinicalCase)
  @JoinTable({
    name: 'learning_path_clinical_cases',
    joinColumn: { name: 'learning_path_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'clinical_case_id', referencedColumnName: 'id' },
  })
  clinical_cases: ClinicalCase[];

  @OneToMany(() => LearningPathProgress, (progress) => progress.learning_path)
  user_progress: LearningPathProgress[];

  @OneToMany(() => LearningPathMilestone, (milestone) => milestone.learning_path)
  milestones: LearningPathMilestone[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
