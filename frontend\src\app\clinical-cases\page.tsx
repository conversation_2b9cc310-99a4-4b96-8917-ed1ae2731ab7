'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Filter, 
  Clock, 
  Users, 
  Target, 
  Brain,
  Stethoscope,
  Heart,
  Activity,
  User,
  BookOpen,
  Play,
  Eye,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';

interface ClinicalCase {
  id: string;
  title: string;
  description: string;
  complexity: 'simple' | 'moderate' | 'complex' | 'expert';
  specialty: string;
  status: 'draft' | 'published' | 'archived';
  estimated_duration_minutes: number;
  learning_objectives: string[];
  tags: string[];
  attempts_count: number;
  average_score: number;
  user_attempts?: any[];
  current_attempt?: any;
}

export default function ClinicalCasesPage() {
  const [cases, setCases] = useState<ClinicalCase[]>([]);
  const [filteredCases, setFilteredCases] = useState<ClinicalCase[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialty, setSelectedSpecialty] = useState('');
  const [selectedComplexity, setSelectedComplexity] = useState('');

  const specialties = [
    'Internal Medicine', 'Cardiology', 'Neurology', 'Pediatrics', 
    'Surgery', 'Emergency', 'Psychiatry', 'Obstetrics', 'Dermatology', 'Radiology'
  ];

  const complexities = ['simple', 'moderate', 'complex', 'expert'];

  useEffect(() => {
    loadCases();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [cases, searchTerm, selectedSpecialty, selectedComplexity]);

  const loadCases = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/clinical-cases', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const casesData = await response.json();
        setCases(casesData);
      } else {
        toast.error('Failed to load clinical cases');
      }
    } catch (error) {
      console.error('Failed to load cases:', error);
      toast.error('Failed to load clinical cases');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...cases];

    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(case_ => 
        case_.title.toLowerCase().includes(searchLower) ||
        case_.description.toLowerCase().includes(searchLower) ||
        case_.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    if (selectedSpecialty) {
      filtered = filtered.filter(case_ => case_.specialty === selectedSpecialty);
    }

    if (selectedComplexity) {
      filtered = filtered.filter(case_ => case_.complexity === selectedComplexity);
    }

    // Only show published cases
    filtered = filtered.filter(case_ => case_.status === 'published');

    setFilteredCases(filtered);
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'simple': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'complex': return 'bg-orange-100 text-orange-800';
      case 'expert': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSpecialtyIcon = (specialty: string) => {
    switch (specialty.toLowerCase()) {
      case 'cardiology': return <Heart className="h-4 w-4" />;
      case 'emergency': return <Activity className="h-4 w-4" />;
      case 'pediatrics': return <User className="h-4 w-4" />;
      default: return <Stethoscope className="h-4 w-4" />;
    }
  };

  const getUserProgress = (case_: ClinicalCase) => {
    if (case_.current_attempt) {
      return {
        status: 'in_progress',
        progress: case_.current_attempt.percentage || 0,
      };
    }
    
    if (case_.user_attempts && case_.user_attempts.length > 0) {
      const completedAttempts = case_.user_attempts.filter(a => a.status === 'completed');
      if (completedAttempts.length > 0) {
        const bestAttempt = completedAttempts.reduce((best, current) => 
          current.percentage > best.percentage ? current : best
        );
        return {
          status: 'completed',
          progress: bestAttempt.percentage,
        };
      }
    }

    return {
      status: 'not_started',
      progress: 0,
    };
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3 mb-8"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <div className="animate-pulse space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="animate-pulse space-y-2">
                    <div className="h-3 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Clinical Cases</h1>
        <p className="text-gray-600">
          Practice with interactive medical scenarios designed to enhance your clinical decision-making skills.
        </p>
      </div>

      {/* Filters */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Cases
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search cases..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
              <SelectTrigger>
                <SelectValue placeholder="All Specialties" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Specialties</SelectItem>
                {specialties.map(specialty => (
                  <SelectItem key={specialty} value={specialty}>{specialty}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedComplexity} onValueChange={setSelectedComplexity}>
              <SelectTrigger>
                <SelectValue placeholder="All Complexities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Complexities</SelectItem>
                {complexities.map(complexity => (
                  <SelectItem key={complexity} value={complexity}>
                    {complexity.charAt(0).toUpperCase() + complexity.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Cases Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCases.map((case_) => {
          const userProgress = getUserProgress(case_);
          
          return (
            <Card key={case_.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg line-clamp-2 mb-2">{case_.title}</CardTitle>
                    <div className="flex items-center gap-2 mb-2">
                      <Badge className={getComplexityColor(case_.complexity)}>
                        {case_.complexity}
                      </Badge>
                      <Badge variant="outline" className="flex items-center gap-1">
                        {getSpecialtyIcon(case_.specialty)}
                        {case_.specialty}
                      </Badge>
                    </div>
                  </div>
                  {userProgress.status === 'completed' && (
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">
                        {userProgress.progress.toFixed(0)}%
                      </div>
                      <p className="text-xs text-gray-600">Best Score</p>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4 line-clamp-3">{case_.description}</p>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{case_.estimated_duration_minutes} min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span>{case_.attempts_count} attempts</span>
                    </div>
                  </div>

                  {case_.attempts_count > 0 && (
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-1">
                        <Target className="h-4 w-4 text-gray-400" />
                        <span>Avg Score: {case_.average_score.toFixed(1)}%</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <TrendingUp className="h-4 w-4 text-gray-400" />
                        <span>Difficulty: {case_.complexity}</span>
                      </div>
                    </div>
                  )}

                  <div className="flex flex-wrap gap-1">
                    {case_.learning_objectives.slice(0, 2).map((objective, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {objective.length > 30 ? `${objective.substring(0, 30)}...` : objective}
                      </Badge>
                    ))}
                    {case_.learning_objectives.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{case_.learning_objectives.length - 2} more
                      </Badge>
                    )}
                  </div>

                  {userProgress.status === 'in_progress' && (
                    <div className="p-2 bg-blue-50 rounded text-center">
                      <p className="text-sm font-medium text-blue-800 mb-1">
                        In Progress - {userProgress.progress.toFixed(0)}%
                      </p>
                      <div className="w-full bg-blue-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${userProgress.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  <div className="flex gap-2 pt-2">
                    <Button asChild className="flex-1">
                      <Link href={`/clinical-cases/${case_.id}`}>
                        <Play className="h-4 w-4 mr-2" />
                        {userProgress.status === 'in_progress' ? 'Continue' : 
                         userProgress.status === 'completed' ? 'Retry' : 'Start Case'}
                      </Link>
                    </Button>
                    <Button variant="outline" asChild>
                      <Link href={`/clinical-cases/${case_.id}/preview`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredCases.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Brain className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium mb-2">No Cases Found</h3>
            <p className="text-gray-600 mb-4">
              {cases.length === 0 
                ? "No clinical cases are currently available."
                : "Try adjusting your filters to find more cases."
              }
            </p>
            {searchTerm || selectedSpecialty || selectedComplexity ? (
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setSelectedSpecialty('');
                  setSelectedComplexity('');
                }}
              >
                Clear Filters
              </Button>
            ) : null}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
