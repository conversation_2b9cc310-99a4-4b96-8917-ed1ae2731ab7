import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { User } from './user.entity';
import { LearningPath } from './learning-path.entity';
import { Course } from './course.entity';
import { LearningGoalProgress } from './learning-goal-progress.entity';

export enum GoalType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  CUSTOM = 'custom',
}

export enum GoalCategory {
  STUDY_TIME = 'study_time',
  COURSE_COMPLETION = 'course_completion',
  ASSESSMENT_SCORE = 'assessment_score',
  SKILL_MASTERY = 'skill_mastery',
  STREAK_MAINTENANCE = 'streak_maintenance',
  LEARNING_PATH = 'learning_path',
  CLINICAL_CASES = 'clinical_cases',
  CUSTOM = 'custom',
}

export enum GoalStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue',
}

export enum GoalPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

@Entity('learning_goals')
export class LearningGoal {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: GoalType,
    default: GoalType.CUSTOM,
  })
  type: GoalType;

  @Column({
    type: 'enum',
    enum: GoalCategory,
    default: GoalCategory.CUSTOM,
  })
  category: GoalCategory;

  @Column({
    type: 'enum',
    enum: GoalStatus,
    default: GoalStatus.ACTIVE,
  })
  status: GoalStatus;

  @Column({
    type: 'enum',
    enum: GoalPriority,
    default: GoalPriority.MEDIUM,
  })
  priority: GoalPriority;

  @Column({ type: 'jsonb' })
  target_criteria: {
    type: 'numeric' | 'percentage' | 'boolean' | 'completion' | 'score';
    target_value: number | boolean | string;
    current_value?: number | boolean | string;
    unit?: string;
    measurement_method?: string;
  };

  @Column({ type: 'timestamp' })
  start_date: Date;

  @Column({ type: 'timestamp' })
  target_date: Date;

  @Column({ type: 'timestamp', nullable: true })
  completed_at: Date;

  @Column({ type: 'uuid', nullable: true })
  learning_path_id: string;

  @Column({ type: 'uuid', nullable: true })
  course_id: string;

  @Column({ type: 'simple-array', nullable: true })
  related_resources: string[];

  @Column({ type: 'jsonb', nullable: true })
  smart_criteria: {
    specific: string;
    measurable: string;
    achievable: string;
    relevant: string;
    time_bound: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  milestones: {
    id: string;
    title: string;
    description: string;
    target_date: Date;
    target_value: number;
    completed: boolean;
    completed_at?: Date;
    order: number;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  reminder_settings: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'custom';
    time: string;
    days_of_week?: number[];
    custom_schedule?: {
      date: Date;
      time: string;
    }[];
  };

  @Column({ type: 'jsonb', nullable: true })
  progress_tracking: {
    auto_tracking: boolean;
    manual_updates: boolean;
    data_sources: string[];
    update_frequency: 'real_time' | 'daily' | 'weekly' | 'manual';
  };

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  progress_percentage: number;

  @Column({ type: 'int', default: 0 })
  streak_count: number;

  @Column({ type: 'timestamp', nullable: true })
  last_progress_update: Date;

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    time_spent_minutes: number;
    sessions_completed: number;
    average_session_duration: number;
    consistency_score: number;
    velocity_trend: 'increasing' | 'stable' | 'decreasing';
    predicted_completion_date?: Date;
  };

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'simple-array', nullable: true })
  tags: string[];

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => LearningPath, { nullable: true })
  @JoinColumn({ name: 'learning_path_id' })
  learning_path: LearningPath;

  @ManyToOne(() => Course, { nullable: true })
  @JoinColumn({ name: 'course_id' })
  course: Course;

  @OneToMany(() => LearningGoalProgress, (progress) => progress.learning_goal)
  progress_entries: LearningGoalProgress[];

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
