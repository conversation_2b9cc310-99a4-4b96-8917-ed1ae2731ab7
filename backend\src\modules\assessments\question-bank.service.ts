import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, ILike } from 'typeorm';
import { Question, QuestionType, QuestionDifficulty, QuestionCategory } from '../../entities/question.entity';
import { Course } from '../../entities/course.entity';
import { Unit } from '../../entities/unit.entity';

export interface CreateQuestionBankDto {
  question_text: string;
  type: QuestionType;
  difficulty: QuestionDifficulty;
  category: QuestionCategory;
  points?: number;
  explanation?: string;
  reference?: string;
  image_url?: string;
  tags?: string[];
  course_id?: string;
  unit_id?: string;
  options: {
    option_text: string;
    is_correct: boolean;
    explanation?: string;
    partial_credit_percentage?: number;
  }[];
}

export interface QuestionBankFilters {
  search?: string;
  type?: QuestionType;
  difficulty?: QuestionDifficulty;
  category?: QuestionCategory;
  course_id?: string;
  unit_id?: string;
  tags?: string[];
  created_by?: string;
  is_active?: boolean;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'ASC' | 'DESC';
}

export interface AdaptiveQuizConfig {
  target_difficulty?: QuestionDifficulty;
  question_count: number;
  categories?: QuestionCategory[];
  exclude_recent?: boolean;
  recent_days?: number;
  adaptive_algorithm?: 'irt' | 'simple' | 'weighted';
}

@Injectable()
export class QuestionBankService {
  constructor(
    @InjectRepository(Question)
    private readonly questionRepository: Repository<Question>,
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(Unit)
    private readonly unitRepository: Repository<Unit>,
  ) {}

  async createQuestion(
    createQuestionDto: CreateQuestionBankDto,
    createdBy: string,
  ): Promise<Question> {
    // Validate course and unit if provided
    if (createQuestionDto.course_id) {
      const course = await this.courseRepository.findOne({
        where: { id: createQuestionDto.course_id },
      });
      if (!course) {
        throw new NotFoundException('Course not found');
      }
    }

    if (createQuestionDto.unit_id) {
      const unit = await this.unitRepository.findOne({
        where: { id: createQuestionDto.unit_id },
      });
      if (!unit) {
        throw new NotFoundException('Unit not found');
      }
    }

    // Validate question options
    this.validateQuestionOptions(createQuestionDto.type, createQuestionDto.options);

    const question = this.questionRepository.create({
      ...createQuestionDto,
      created_by: createdBy,
      is_active: true,
      usage_count: 0,
      average_score: 0,
      difficulty_index: this.getDifficultyIndex(createQuestionDto.difficulty),
      discrimination_index: 0,
    });

    return await this.questionRepository.save(question);
  }

  async findQuestions(filters: QuestionBankFilters = {}) {
    const {
      search,
      type,
      difficulty,
      category,
      course_id,
      unit_id,
      tags,
      created_by,
      is_active = true,
      page = 1,
      limit = 20,
      sort_by = 'created_at',
      sort_order = 'DESC',
    } = filters;

    const queryBuilder = this.questionRepository
      .createQueryBuilder('question')
      .leftJoinAndSelect('question.course', 'course')
      .leftJoinAndSelect('question.unit', 'unit')
      .where('question.is_active = :is_active', { is_active });

    // Apply filters
    if (search) {
      queryBuilder.andWhere(
        '(question.question_text ILIKE :search OR question.tags && ARRAY[:search])',
        { search: `%${search}%` },
      );
    }

    if (type) {
      queryBuilder.andWhere('question.type = :type', { type });
    }

    if (difficulty) {
      queryBuilder.andWhere('question.difficulty = :difficulty', { difficulty });
    }

    if (category) {
      queryBuilder.andWhere('question.category = :category', { category });
    }

    if (course_id) {
      queryBuilder.andWhere('question.course_id = :course_id', { course_id });
    }

    if (unit_id) {
      queryBuilder.andWhere('question.unit_id = :unit_id', { unit_id });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere('question.tags && ARRAY[:...tags]', { tags });
    }

    if (created_by) {
      queryBuilder.andWhere('question.created_by = :created_by', { created_by });
    }

    // Apply sorting
    queryBuilder.orderBy(`question.${sort_by}`, sort_order);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [questions, total] = await queryBuilder.getManyAndCount();

    return {
      questions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async generateAdaptiveQuiz(
    userId: string,
    config: AdaptiveQuizConfig,
  ): Promise<Question[]> {
    const {
      target_difficulty,
      question_count,
      categories,
      exclude_recent = true,
      recent_days = 7,
      adaptive_algorithm = 'weighted',
    } = config;

    let queryBuilder = this.questionRepository
      .createQueryBuilder('question')
      .where('question.is_active = :is_active', { is_active: true });

    // Filter by categories if specified
    if (categories && categories.length > 0) {
      queryBuilder.andWhere('question.category IN (:...categories)', { categories });
    }

    // Exclude recently answered questions
    if (exclude_recent) {
      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - recent_days);
      
      queryBuilder.andWhere(`
        question.id NOT IN (
          SELECT DISTINCT answer.question_id 
          FROM assessment_answers answer 
          WHERE answer.user_id = :userId 
          AND answer.created_at > :recentDate
        )
      `, { userId, recentDate });
    }

    // Apply adaptive algorithm
    switch (adaptive_algorithm) {
      case 'irt':
        // Item Response Theory - more complex algorithm
        queryBuilder.orderBy('RANDOM()'); // Simplified for now
        break;
      case 'weighted':
        // Weighted selection based on difficulty and performance
        queryBuilder.addSelect(`
          CASE 
            WHEN question.difficulty_index BETWEEN 0.3 AND 0.7 THEN 3
            WHEN question.difficulty_index BETWEEN 0.2 AND 0.8 THEN 2
            ELSE 1
          END as weight
        `);
        queryBuilder.orderBy('weight', 'DESC');
        queryBuilder.addOrderBy('RANDOM()');
        break;
      default:
        // Simple random selection
        queryBuilder.orderBy('RANDOM()');
    }

    queryBuilder.take(question_count);

    return await queryBuilder.getMany();
  }

  async updateQuestionStatistics(
    questionId: string,
    isCorrect: boolean,
    responseTime: number,
  ): Promise<void> {
    const question = await this.questionRepository.findOne({
      where: { id: questionId },
    });

    if (!question) {
      throw new NotFoundException('Question not found');
    }

    // Update usage count
    question.usage_count += 1;

    // Update average score (simplified calculation)
    const currentScore = isCorrect ? 1 : 0;
    question.average_score = 
      (question.average_score * (question.usage_count - 1) + currentScore) / question.usage_count;

    // Update difficulty index based on performance
    question.difficulty_index = 1 - question.average_score;

    await this.questionRepository.save(question);
  }

  private validateQuestionOptions(type: QuestionType, options: any[]): void {
    if (!options || options.length === 0) {
      throw new BadRequestException('Question must have at least one option');
    }

    const correctOptions = options.filter(opt => opt.is_correct);

    switch (type) {
      case QuestionType.MULTIPLE_CHOICE:
        if (correctOptions.length !== 1) {
          throw new BadRequestException('Multiple choice questions must have exactly one correct answer');
        }
        if (options.length < 2) {
          throw new BadRequestException('Multiple choice questions must have at least 2 options');
        }
        break;
      case QuestionType.MULTIPLE_SELECT:
        if (correctOptions.length < 1) {
          throw new BadRequestException('Multiple select questions must have at least one correct answer');
        }
        break;
      case QuestionType.TRUE_FALSE:
        if (options.length !== 2) {
          throw new BadRequestException('True/false questions must have exactly 2 options');
        }
        if (correctOptions.length !== 1) {
          throw new BadRequestException('True/false questions must have exactly one correct answer');
        }
        break;
    }
  }

  private getDifficultyIndex(difficulty: QuestionDifficulty): number {
    switch (difficulty) {
      case QuestionDifficulty.EASY:
        return 0.2;
      case QuestionDifficulty.MEDIUM:
        return 0.5;
      case QuestionDifficulty.HARD:
        return 0.8;
      default:
        return 0.5;
    }
  }
}
