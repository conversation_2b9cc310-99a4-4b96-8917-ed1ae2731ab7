import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { LearningPathIntegrationService } from './learning-path-integration.service';

@Injectable()
export class LearningPathEventsService {
  private readonly logger = new Logger(LearningPathEventsService.name);

  constructor(
    private integrationService: LearningPathIntegrationService,
  ) {}

  @OnEvent('course.progress.updated')
  async handleCourseProgressUpdated(payload: {
    userId: string;
    courseId: string;
    progressData: any;
  }) {
    this.logger.log(`Handling course progress update for user ${payload.userId}, course ${payload.courseId}`);
    
    try {
      await this.integrationService.syncCourseProgress(
        payload.userId,
        payload.courseId,
        payload.progressData
      );
    } catch (error) {
      this.logger.error(`Error handling course progress update: ${error.message}`, error.stack);
    }
  }

  @OnEvent('course.completed')
  async handleCourseCompleted(payload: {
    userId: string;
    courseId: string;
    completionData: any;
  }) {
    this.logger.log(`Handling course completion for user ${payload.userId}, course ${payload.courseId}`);
    
    try {
      await this.integrationService.syncCourseProgress(
        payload.userId,
        payload.courseId,
        { ...payload.completionData, status: 'completed' }
      );
    } catch (error) {
      this.logger.error(`Error handling course completion: ${error.message}`, error.stack);
    }
  }

  @OnEvent('course.enrolled')
  async handleCourseEnrolled(payload: {
    userId: string;
    courseId: string;
    enrollmentData: any;
  }) {
    this.logger.log(`Handling course enrollment for user ${payload.userId}, course ${payload.courseId}`);
    
    try {
      // Auto-enroll in recommended learning paths
      await this.integrationService.autoEnrollInRecommendedPaths(
        payload.userId,
        payload.courseId
      );
    } catch (error) {
      this.logger.error(`Error handling course enrollment: ${error.message}`, error.stack);
    }
  }

  @OnEvent('assessment.completed')
  async handleAssessmentCompleted(payload: {
    userId: string;
    assessmentId: string;
    attemptData: any;
  }) {
    this.logger.log(`Handling assessment completion for user ${payload.userId}, assessment ${payload.assessmentId}`);
    
    try {
      await this.integrationService.syncAssessmentResults(
        payload.userId,
        payload.assessmentId,
        payload.attemptData
      );
    } catch (error) {
      this.logger.error(`Error handling assessment completion: ${error.message}`, error.stack);
    }
  }

  @OnEvent('clinical-case.completed')
  async handleClinicalCaseCompleted(payload: {
    userId: string;
    caseId: string;
    attemptData: any;
  }) {
    this.logger.log(`Handling clinical case completion for user ${payload.userId}, case ${payload.caseId}`);
    
    try {
      await this.integrationService.syncClinicalCaseCompletion(
        payload.userId,
        payload.caseId,
        payload.attemptData
      );
    } catch (error) {
      this.logger.error(`Error handling clinical case completion: ${error.message}`, error.stack);
    }
  }

  @OnEvent('learning-path.enrolled')
  async handleLearningPathEnrolled(payload: {
    userId: string;
    pathId: string;
    enrollmentData: any;
  }) {
    this.logger.log(`Handling learning path enrollment for user ${payload.userId}, path ${payload.pathId}`);
    
    try {
      // Create goals from path milestones
      await this.integrationService.createGoalsFromPathMilestones(
        payload.userId,
        payload.pathId
      );
    } catch (error) {
      this.logger.error(`Error handling learning path enrollment: ${error.message}`, error.stack);
    }
  }

  @OnEvent('learning-path.milestone.achieved')
  async handleMilestoneAchieved(payload: {
    userId: string;
    pathId: string;
    milestoneId: string;
    achievementData: any;
  }) {
    this.logger.log(`Handling milestone achievement for user ${payload.userId}, milestone ${payload.milestoneId}`);
    
    try {
      // Update related goals
      // This could trigger goal completion or milestone progress
      // Implementation depends on specific business logic
    } catch (error) {
      this.logger.error(`Error handling milestone achievement: ${error.message}`, error.stack);
    }
  }

  @OnEvent('learning-goal.completed')
  async handleGoalCompleted(payload: {
    userId: string;
    goalId: string;
    completionData: any;
  }) {
    this.logger.log(`Handling goal completion for user ${payload.userId}, goal ${payload.goalId}`);
    
    try {
      // Could trigger learning path progress updates or recommendations
      // Implementation depends on specific business logic
    } catch (error) {
      this.logger.error(`Error handling goal completion: ${error.message}`, error.stack);
    }
  }

  @OnEvent('user.study.session')
  async handleStudySession(payload: {
    userId: string;
    sessionData: {
      duration_minutes: number;
      materials_covered: string[];
      courses_covered: string[];
      assessments_completed: string[];
      clinical_cases_completed: string[];
    };
  }) {
    this.logger.log(`Handling study session for user ${payload.userId}`);
    
    try {
      // Update learning path progress based on study session
      const { sessionData } = payload;
      
      // Sync course progress
      for (const courseId of sessionData.courses_covered || []) {
        await this.integrationService.syncCourseProgress(
          payload.userId,
          courseId,
          { time_spent_minutes: sessionData.duration_minutes }
        );
      }
      
      // Sync assessment results
      for (const assessmentId of sessionData.assessments_completed || []) {
        await this.integrationService.syncAssessmentResults(
          payload.userId,
          assessmentId,
          { completed: true, time_taken_minutes: sessionData.duration_minutes }
        );
      }
      
      // Sync clinical case completions
      for (const caseId of sessionData.clinical_cases_completed || []) {
        await this.integrationService.syncClinicalCaseCompletion(
          payload.userId,
          caseId,
          { completed: true, time_spent_minutes: sessionData.duration_minutes }
        );
      }
    } catch (error) {
      this.logger.error(`Error handling study session: ${error.message}`, error.stack);
    }
  }
}
