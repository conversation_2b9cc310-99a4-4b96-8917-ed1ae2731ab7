'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  BookOpen, 
  Lock,
  Unlock,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';

interface PrerequisiteCourse {
  id: string;
  title: string;
  code: string;
  is_completed?: boolean;
}

interface Prerequisites {
  course_ids: string[];
  skills: string[];
  description: string;
  prerequisite_courses: PrerequisiteCourse[];
}

interface PrerequisitesCheckerProps {
  courseId: string;
  onPrerequisitesMet?: (met: boolean) => void;
}

export function PrerequisitesChecker({ courseId, onPrerequisitesMet }: PrerequisitesCheckerProps) {
  const [prerequisites, setPrerequisites] = useState<Prerequisites | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userEnrollments, setUserEnrollments] = useState<any[]>([]);

  useEffect(() => {
    loadPrerequisites();
    loadUserEnrollments();
  }, [courseId]);

  const loadPrerequisites = async () => {
    try {
      const response = await fetch(`/api/courses/${courseId}/prerequisites`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPrerequisites(data);
      } else {
        setError('Failed to load prerequisites');
      }
    } catch (error) {
      console.error('Failed to load prerequisites:', error);
      setError('Failed to load prerequisites');
    }
  };

  const loadUserEnrollments = async () => {
    try {
      const response = await fetch('/api/courses/my-courses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserEnrollments(data);
      }
    } catch (error) {
      console.error('Failed to load user enrollments:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkCourseCompletion = (courseId: string): boolean => {
    return userEnrollments.some(
      enrollment => enrollment.course_id === courseId && enrollment.status === 'completed'
    );
  };

  const getPrerequisiteStatus = () => {
    if (!prerequisites) return { met: false, total: 0, completed: 0 };

    const coursePrereqs = prerequisites.course_ids || [];
    const completedPrereqs = coursePrereqs.filter(id => checkCourseCompletion(id));

    return {
      met: coursePrereqs.length === 0 || completedPrereqs.length === coursePrereqs.length,
      total: coursePrereqs.length,
      completed: completedPrereqs.length,
    };
  };

  useEffect(() => {
    if (prerequisites && userEnrollments.length > 0) {
      const status = getPrerequisiteStatus();
      onPrerequisitesMet?.(status.met);
    }
  }, [prerequisites, userEnrollments, onPrerequisitesMet]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-2">
            <div className="h-3 bg-gray-200 rounded"></div>
            <div className="h-3 bg-gray-200 rounded w-5/6"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-600">
            <XCircle className="h-5 w-5" />
            <span>{error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!prerequisites || (prerequisites.course_ids.length === 0 && prerequisites.skills.length === 0)) {
    return (
      <Card className="border-green-200">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-green-600">
            <Unlock className="h-5 w-5" />
            <span>No prerequisites required for this course</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const status = getPrerequisiteStatus();

  return (
    <Card className={`${status.met ? 'border-green-200' : 'border-yellow-200'}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {status.met ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <Lock className="h-5 w-5 text-yellow-500" />
          )}
          Prerequisites
          <Badge variant={status.met ? 'default' : 'secondary'} className="ml-auto">
            {status.completed}/{status.total} completed
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {prerequisites.description && (
          <p className="text-sm text-gray-600">{prerequisites.description}</p>
        )}

        {/* Course Prerequisites */}
        {prerequisites.course_ids.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Required Courses</h4>
            <div className="space-y-2">
              {prerequisites.prerequisite_courses.map((course) => {
                const isCompleted = checkCourseCompletion(course.id);
                return (
                  <div
                    key={course.id}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      {isCompleted ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-gray-400" />
                      )}
                      <div>
                        <p className="font-medium text-gray-900">{course.title}</p>
                        <p className="text-sm text-gray-500">{course.code}</p>
                      </div>
                    </div>
                    {!isCompleted && (
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/courses/${course.id}`}>
                          View Course <ArrowRight className="h-4 w-4 ml-1" />
                        </Link>
                      </Button>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Skill Prerequisites */}
        {prerequisites.skills.length > 0 && (
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Required Skills</h4>
            <div className="flex flex-wrap gap-2">
              {prerequisites.skills.map((skill, index) => (
                <Badge key={index} variant="outline">
                  {skill}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Status Message */}
        <div className={`p-3 rounded-lg ${
          status.met 
            ? 'bg-green-50 border border-green-200' 
            : 'bg-yellow-50 border border-yellow-200'
        }`}>
          <div className="flex items-center gap-2">
            {status.met ? (
              <>
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-green-700 font-medium">
                  All prerequisites met! You can enroll in this course.
                </span>
              </>
            ) : (
              <>
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                <span className="text-yellow-700 font-medium">
                  Complete the required courses above before enrolling.
                </span>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
