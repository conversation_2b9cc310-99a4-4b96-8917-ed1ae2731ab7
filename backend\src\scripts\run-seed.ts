import { DataSource } from 'typeorm';
import { seedCourses } from './seed-courses';
import { seedClinicalCases } from './seed-clinical-cases';
import { Course } from '../entities/course.entity';
import { CourseCategory } from '../entities/course-category.entity';
import { CourseEnrollment } from '../entities/course-enrollment.entity';
import { ClinicalCase } from '../entities/clinical-case.entity';
import { CaseAttempt } from '../entities/case-attempt.entity';
import { User } from '../entities/user.entity';
import { Unit } from '../entities/unit.entity';

const dataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_NAME || 'medtrack',
  entities: [
    Course,
    CourseCategory,
    CourseEnrollment,
    ClinicalCase,
    CaseAttempt,
    User,
    Unit,
  ],
  synchronize: false,
  logging: false,
});

async function runSeed() {
  try {
    console.log('🚀 Initializing database connection...');
    await dataSource.initialize();
    console.log('✅ Database connected successfully');

    await seedCourses(dataSource);
    await seedClinicalCases(dataSource);

    console.log('🎉 Seeding completed successfully!');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await dataSource.destroy();
    console.log('🔌 Database connection closed');
  }
}

runSeed();
