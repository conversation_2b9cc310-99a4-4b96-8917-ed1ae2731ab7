'use client';

import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  Circle, 
  Lock, 
  Play, 
  BookOpen, 
  FileText, 
  Award, 
  Clock,
  Target,
  TrendingUp,
  Users,
  Star
} from 'lucide-react';

interface Module {
  id: string;
  title: string;
  description: string;
  type: 'course' | 'assessment' | 'clinical_case' | 'milestone' | 'custom';
  resource_id?: string;
  estimated_duration_hours: number;
  is_required: boolean;
  unlock_conditions?: string[];
  order: number;
}

interface Phase {
  id: string;
  title: string;
  description: string;
  order: number;
  estimated_duration_weeks: number;
  modules: Module[];
}

interface LearningPath {
  id: string;
  title: string;
  description: string;
  path_structure: {
    phases: Phase[];
  };
  milestones: Milestone[];
}

interface Milestone {
  id: string;
  title: string;
  description: string;
  type: string;
  order: number;
  is_required: boolean;
  rewards?: {
    points?: number;
    badge_id?: string;
    certificate?: any;
  };
}

interface ModuleProgress {
  module_id: string;
  phase_id: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  progress_percentage: number;
  time_spent_minutes: number;
  best_score?: number;
}

interface PhaseProgress {
  phase_id: string;
  status: 'not_started' | 'in_progress' | 'completed';
  progress_percentage: number;
  modules_completed: string[];
  current_module_id?: string;
}

interface LearningPathProgress {
  overall_progress_percentage: number;
  current_phase_index: number;
  current_module_index: number;
  phase_progress: PhaseProgress[];
  module_progress: ModuleProgress[];
  milestones_achieved: { milestone_id: string; achieved_at: string }[];
}

interface LearningPathVisualizationProps {
  pathId: string;
}

export const LearningPathVisualization: React.FC<LearningPathVisualizationProps> = ({ pathId }) => {
  const [learningPath, setLearningPath] = useState<LearningPath | null>(null);
  const [progress, setProgress] = useState<LearningPathProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPhase, setSelectedPhase] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'overview' | 'detailed'>('overview');

  useEffect(() => {
    fetchLearningPath();
    fetchProgress();
  }, [pathId]);

  const fetchLearningPath = async () => {
    try {
      const response = await fetch(`/api/learning-paths/${pathId}`);
      const data = await response.json();
      setLearningPath(data);
    } catch (error) {
      console.error('Error fetching learning path:', error);
    }
  };

  const fetchProgress = async () => {
    try {
      const response = await fetch(`/api/learning-paths/${pathId}/progress`);
      const data = await response.json();
      setProgress(data);
    } catch (error) {
      console.error('Error fetching progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const getModuleProgress = (moduleId: string): ModuleProgress | undefined => {
    return progress?.module_progress.find(mp => mp.module_id === moduleId);
  };

  const getPhaseProgress = (phaseId: string): PhaseProgress | undefined => {
    return progress?.phase_progress.find(pp => pp.phase_id === phaseId);
  };

  const isMilestoneAchieved = (milestoneId: string): boolean => {
    return progress?.milestones_achieved.some(ma => ma.milestone_id === milestoneId) || false;
  };

  const isModuleUnlocked = (module: Module, phase: Phase): boolean => {
    if (!module.unlock_conditions || module.unlock_conditions.length === 0) {
      return true;
    }

    // Check if all prerequisite modules are completed
    return module.unlock_conditions.every(conditionId => {
      const moduleProgress = getModuleProgress(conditionId);
      return moduleProgress?.status === 'completed';
    });
  };

  const getModuleIcon = (type: string) => {
    switch (type) {
      case 'course': return BookOpen;
      case 'assessment': return FileText;
      case 'clinical_case': return Users;
      case 'milestone': return Award;
      default: return Circle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'not_started': return 'text-gray-400 bg-gray-100';
      case 'skipped': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-400 bg-gray-100';
    }
  };

  const renderOverviewMode = () => {
    if (!learningPath || !progress) return null;

    return (
      <div className="space-y-8">
        {/* Progress Summary */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Overall Progress</h3>
            <span className="text-2xl font-bold text-blue-600">
              {Math.round(progress.overall_progress_percentage)}%
            </span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div 
              className="bg-blue-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${progress.overall_progress_percentage}%` }}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {progress.phase_progress.filter(p => p.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-500">Phases Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {progress.module_progress.filter(m => m.status === 'completed').length}
              </div>
              <div className="text-sm text-gray-500">Modules Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {progress.milestones_achieved.length}
              </div>
              <div className="text-sm text-gray-500">Milestones Achieved</div>
            </div>
          </div>
        </div>

        {/* Phase Timeline */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Learning Journey</h3>
          
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200"></div>
            
            {learningPath.path_structure.phases.map((phase, index) => {
              const phaseProgress = getPhaseProgress(phase.id);
              const isActive = progress.current_phase_index === index;
              const isCompleted = phaseProgress?.status === 'completed';
              
              return (
                <div key={phase.id} className="relative flex items-start mb-8 last:mb-0">
                  {/* Timeline dot */}
                  <div className={`relative z-10 flex items-center justify-center w-16 h-16 rounded-full border-4 ${
                    isCompleted 
                      ? 'bg-green-100 border-green-500' 
                      : isActive 
                        ? 'bg-blue-100 border-blue-500' 
                        : 'bg-gray-100 border-gray-300'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="w-8 h-8 text-green-600" />
                    ) : isActive ? (
                      <Play className="w-8 h-8 text-blue-600" />
                    ) : (
                      <Circle className="w-8 h-8 text-gray-400" />
                    )}
                  </div>
                  
                  {/* Phase content */}
                  <div className="ml-6 flex-1">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-lg font-medium text-gray-900">{phase.title}</h4>
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-500">{phase.estimated_duration_weeks}w</span>
                        </div>
                      </div>
                      
                      <p className="text-gray-600 mb-3">{phase.description}</p>
                      
                      {phaseProgress && (
                        <div className="mb-3">
                          <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{Math.round(phaseProgress.progress_percentage)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${phaseProgress.progress_percentage}%` }}
                            />
                          </div>
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-500">
                          {phase.modules.length} modules
                        </div>
                        <button
                          onClick={() => setSelectedPhase(selectedPhase === phase.id ? null : phase.id)}
                          className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                        >
                          {selectedPhase === phase.id ? 'Hide Details' : 'View Details'}
                        </button>
                      </div>
                      
                      {/* Module details */}
                      {selectedPhase === phase.id && (
                        <div className="mt-4 space-y-2">
                          {phase.modules.map(module => {
                            const moduleProgress = getModuleProgress(module.id);
                            const isUnlocked = isModuleUnlocked(module, phase);
                            const Icon = getModuleIcon(module.type);
                            
                            return (
                              <div key={module.id} className="flex items-center space-x-3 p-2 bg-white rounded border">
                                <div className={`p-2 rounded ${getStatusColor(moduleProgress?.status || 'not_started')}`}>
                                  {isUnlocked ? <Icon className="w-4 h-4" /> : <Lock className="w-4 h-4" />}
                                </div>
                                
                                <div className="flex-1">
                                  <div className="font-medium text-gray-900">{module.title}</div>
                                  <div className="text-sm text-gray-500">
                                    {module.estimated_duration_hours}h • {module.type.replace('_', ' ')}
                                  </div>
                                </div>
                                
                                {moduleProgress && (
                                  <div className="text-right">
                                    <div className="text-sm font-medium text-gray-900">
                                      {Math.round(moduleProgress.progress_percentage)}%
                                    </div>
                                    {moduleProgress.best_score && (
                                      <div className="text-xs text-gray-500">
                                        Best: {moduleProgress.best_score}%
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Milestones */}
        {learningPath.milestones && learningPath.milestones.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Milestones</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {learningPath.milestones.map(milestone => {
                const isAchieved = isMilestoneAchieved(milestone.id);
                
                return (
                  <div key={milestone.id} className={`p-4 rounded-lg border-2 ${
                    isAchieved 
                      ? 'border-green-200 bg-green-50' 
                      : 'border-gray-200 bg-gray-50'
                  }`}>
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`p-2 rounded-full ${
                        isAchieved ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                      }`}>
                        <Award className="w-5 h-5" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{milestone.title}</h4>
                        {milestone.rewards?.points && (
                          <div className="text-sm text-gray-500">
                            {milestone.rewards.points} points
                          </div>
                        )}
                      </div>
                      {isAchieved && (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{milestone.description}</p>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!learningPath) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Learning path not found</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">{learningPath.title}</h1>
        <p className="text-gray-600">{learningPath.description}</p>
      </div>

      {renderOverviewMode()}
    </div>
  );
};
