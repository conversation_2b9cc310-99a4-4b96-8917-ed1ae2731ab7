import { DataSource } from 'typeorm';
import { ClinicalCase, CaseComplexity, CaseSpecialty, CaseStatus } from '../entities/clinical-case.entity';
import { User, UserRole } from '../entities/user.entity';

// Sample clinical cases with realistic medical scenarios
const clinicalCases = [
  {
    title: 'Acute Chest Pain in Emergency Department',
    description: 'A 55-year-old male presents to the ED with sudden onset chest pain. Navigate through history taking, physical examination, diagnostic workup, and treatment decisions.',
    complexity: CaseComplexity.MODERATE,
    specialty: CaseSpecialty.EMERGENCY,
    estimated_duration_minutes: 45,
    learning_objectives: [
      'Perform systematic evaluation of chest pain',
      'Differentiate between cardiac and non-cardiac causes',
      'Order appropriate diagnostic tests',
      'Implement evidence-based treatment protocols',
      'Recognize when to consult cardiology'
    ],
    tags: ['chest pain', 'emergency', 'cardiology', 'diagnosis', 'ECG'],
    patient_info: {
      age: 55,
      gender: 'Male',
      chief_complaint: 'Sudden onset chest pain for 2 hours',
      history_of_present_illness: 'Patient reports crushing chest pain that started while watching TV. Pain radiates to left arm and jaw. Associated with nausea and diaphoresis. No relief with rest.',
      past_medical_history: ['Hypertension', 'Hyperlipidemia', 'Type 2 Diabetes'],
      medications: ['Lisinopril 10mg daily', 'Metformin 1000mg BID', 'Atorvastatin 40mg daily'],
      allergies: ['NKDA'],
      social_history: '30 pack-year smoking history, quit 5 years ago. Occasional alcohol use.',
      family_history: 'Father died of MI at age 60, mother has diabetes',
      vital_signs: {
        temperature: '98.6°F',
        blood_pressure: '160/95 mmHg',
        heart_rate: '95 bpm',
        respiratory_rate: '20/min',
        oxygen_saturation: '98% on room air'
      }
    },
    case_flow: {
      sections: [
        {
          id: 'history',
          type: 'history',
          title: 'History Taking',
          content: '<p>You are evaluating a 55-year-old male who presents with chest pain. Begin by taking a focused history.</p><p><strong>Chief Complaint:</strong> "I have crushing chest pain that started 2 hours ago."</p>',
          order: 1,
          is_unlocked_initially: true,
          points: 20
        },
        {
          id: 'physical_exam',
          type: 'physical_exam',
          title: 'Physical Examination',
          content: '<p>Perform a focused cardiovascular and pulmonary examination.</p><p><strong>General:</strong> Patient appears uncomfortable, diaphoretic</p><p><strong>Cardiovascular:</strong> Regular rate and rhythm, no murmurs, rubs, or gallops</p><p><strong>Pulmonary:</strong> Clear to auscultation bilaterally</p>',
          order: 2,
          is_unlocked_initially: false,
          unlock_conditions: ['history_complete'],
          points: 25
        },
        {
          id: 'diagnostics',
          type: 'diagnostics',
          title: 'Diagnostic Workup',
          content: '<p>Order appropriate diagnostic tests based on your clinical assessment.</p>',
          order: 3,
          is_unlocked_initially: false,
          unlock_conditions: ['physical_exam_complete'],
          points: 30
        },
        {
          id: 'treatment',
          type: 'treatment',
          title: 'Treatment Plan',
          content: '<p>Based on your findings, develop an appropriate treatment plan.</p>',
          order: 4,
          is_unlocked_initially: false,
          unlock_conditions: ['diagnostics_complete'],
          points: 25
        }
      ],
      decision_points: [
        {
          id: 'history_focus',
          section_id: 'history',
          question: 'What is the most important aspect of the history to focus on first?',
          options: [
            {
              id: 'pain_characteristics',
              text: 'Detailed pain characteristics (quality, location, radiation, timing)',
              is_correct: true,
              feedback: 'Excellent! Characterizing chest pain is crucial for differential diagnosis.',
              points: 10,
              unlocks_sections: ['physical_exam']
            },
            {
              id: 'family_history',
              text: 'Family history of cardiac disease',
              is_correct: false,
              feedback: 'While important, pain characteristics should be assessed first in acute presentation.',
              points: 5
            },
            {
              id: 'social_history',
              text: 'Smoking and alcohol history',
              is_correct: false,
              feedback: 'Risk factors are important but secondary to acute symptom assessment.',
              points: 3
            }
          ],
          type: 'single_choice',
          required: true
        },
        {
          id: 'diagnostic_priority',
          section_id: 'diagnostics',
          question: 'What is the most urgent diagnostic test to order?',
          options: [
            {
              id: 'ecg',
              text: '12-lead ECG',
              is_correct: true,
              feedback: 'Correct! ECG should be obtained within 10 minutes of presentation for chest pain.',
              points: 15,
              consequences: ['ECG shows ST-elevation in leads II, III, aVF suggesting inferior STEMI']
            },
            {
              id: 'chest_xray',
              text: 'Chest X-ray',
              is_correct: false,
              feedback: 'Chest X-ray is useful but ECG takes priority in acute chest pain.',
              points: 8
            },
            {
              id: 'troponin',
              text: 'Cardiac troponin',
              is_correct: false,
              feedback: 'Troponin is important but ECG should be done first for immediate risk stratification.',
              points: 10
            }
          ],
          type: 'single_choice',
          required: true
        }
      ]
    },
    diagnostic_criteria: {
      primary_diagnosis: 'ST-Elevation Myocardial Infarction (STEMI)',
      differential_diagnoses: [
        'Unstable angina',
        'Non-STEMI',
        'Aortic dissection',
        'Pulmonary embolism',
        'Pericarditis'
      ],
      key_findings: [
        'ST-elevation on ECG',
        'Typical chest pain presentation',
        'Risk factors present',
        'Elevated cardiac biomarkers'
      ]
    }
  },
  {
    title: 'Pediatric Fever and Rash',
    description: 'A 4-year-old child presents with fever and a characteristic rash. Work through the diagnostic process and management decisions.',
    complexity: CaseComplexity.SIMPLE,
    specialty: CaseSpecialty.PEDIATRICS,
    estimated_duration_minutes: 30,
    learning_objectives: [
      'Recognize common pediatric rashes',
      'Perform age-appropriate physical examination',
      'Understand fever management in children',
      'Identify when to involve parents in decision-making'
    ],
    tags: ['pediatrics', 'fever', 'rash', 'infectious disease'],
    patient_info: {
      age: 4,
      gender: 'Female',
      chief_complaint: 'Fever and rash for 2 days',
      history_of_present_illness: 'Parents report fever up to 102°F for 2 days. Red rash appeared yesterday, starting on face and spreading to body. Child has been less active but still eating and drinking.',
      past_medical_history: ['Up to date on vaccinations'],
      medications: ['Children\'s Tylenol as needed'],
      allergies: ['NKDA'],
      social_history: 'Attends daycare, no recent travel',
      family_history: 'No significant family history',
      vital_signs: {
        temperature: '101.8°F',
        blood_pressure: '95/60 mmHg',
        heart_rate: '110 bpm',
        respiratory_rate: '24/min',
        oxygen_saturation: '99% on room air'
      }
    },
    case_flow: {
      sections: [
        {
          id: 'history',
          type: 'history',
          title: 'History and Review of Systems',
          content: '<p>Gather history from parents about the child\'s symptoms and recent exposures.</p>',
          order: 1,
          is_unlocked_initially: true,
          points: 15
        },
        {
          id: 'physical_exam',
          type: 'physical_exam',
          title: 'Physical Examination',
          content: '<p>Perform a gentle, systematic examination of the child.</p><p><strong>General:</strong> Alert, interactive child, appears mildly ill</p><p><strong>HEENT:</strong> Conjunctival injection, strawberry tongue</p><p><strong>Skin:</strong> Erythematous maculopapular rash on face, trunk, and extremities</p>',
          order: 2,
          is_unlocked_initially: false,
          points: 20
        },
        {
          id: 'diagnosis',
          type: 'diagnostics',
          title: 'Clinical Diagnosis',
          content: '<p>Based on your clinical findings, what is your diagnosis?</p>',
          order: 3,
          is_unlocked_initially: false,
          points: 25
        },
        {
          id: 'treatment',
          type: 'treatment',
          title: 'Management Plan',
          content: '<p>Develop an appropriate treatment and follow-up plan.</p>',
          order: 4,
          is_unlocked_initially: false,
          points: 20
        }
      ],
      decision_points: [
        {
          id: 'rash_assessment',
          section_id: 'physical_exam',
          question: 'What additional physical finding would you look for to support your suspected diagnosis?',
          options: [
            {
              id: 'lymph_nodes',
              text: 'Cervical lymphadenopathy',
              is_correct: true,
              feedback: 'Correct! Cervical lymphadenopathy is a key finding in this condition.',
              points: 10
            },
            {
              id: 'heart_murmur',
              text: 'Heart murmur',
              is_correct: false,
              feedback: 'While cardiac involvement can occur, lymphadenopathy is more characteristic.',
              points: 5
            },
            {
              id: 'abdominal_mass',
              text: 'Abdominal organomegaly',
              is_correct: false,
              feedback: 'This is not typically associated with the presenting symptoms.',
              points: 2
            }
          ],
          type: 'single_choice',
          required: true
        }
      ]
    }
  }
];

export async function seedClinicalCases(dataSource: DataSource) {
  const clinicalCaseRepository = dataSource.getRepository(ClinicalCase);
  const userRepository = dataSource.getRepository(User);

  console.log('🏥 Seeding clinical cases...');

  // Find or create instructor user
  let instructor = await userRepository.findOne({
    where: { email: '<EMAIL>' }
  });

  if (!instructor) {
    instructor = userRepository.create({
      email: '<EMAIL>',
      name: 'Dr. Sarah Johnson',
      role: UserRole.TEACHER,
      is_verified: true,
    });
    instructor = await userRepository.save(instructor);
    console.log('✅ Created instructor user for clinical cases');
  }

  // Create clinical cases
  const createdCases = [];
  for (const caseData of clinicalCases) {
    const existingCase = await clinicalCaseRepository.findOne({
      where: { title: caseData.title }
    });

    if (!existingCase) {
      const clinicalCase = clinicalCaseRepository.create({
        ...caseData,
        created_by: instructor.id,
        status: CaseStatus.PUBLISHED,
      });

      const savedCase = await clinicalCaseRepository.save(clinicalCase);
      createdCases.push(savedCase);
      console.log(`✅ Created clinical case: ${savedCase.title}`);
    } else {
      createdCases.push(existingCase);
      console.log(`⏭️  Clinical case already exists: ${existingCase.title}`);
    }
  }

  console.log('🎉 Clinical cases seeding completed!');
  console.log(`📊 Created ${createdCases.length} clinical cases`);
}
