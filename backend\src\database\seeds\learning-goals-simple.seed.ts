import { DataSource } from 'typeorm';

export async function seedLearningGoalsSimple(dataSource: DataSource) {
  console.log('Starting simple learning goals seeding...');
  
  try {
    // Get existing users (we'll use the first few for demo goals)
    const users = await dataSource.query(`
      SELECT id FROM users LIMIT 3;
    `);

    if (users.length === 0) {
      console.log('No users found, skipping goals seeding');
      return;
    }

    // Get existing learning paths
    const learningPaths = await dataSource.query(`
      SELECT id FROM learning_paths LIMIT 3;
    `);

    // Create sample learning goals
    const goals = [
      {
        title: 'Achieve USMLE Step 1 Score of 240+',
        description: 'Target score of 240 or higher on USMLE Step 1 examination',
        type: 'custom',
        category: 'assessment_score',
        priority: 'high',
        target_date: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000), // 6 months from now
        target_criteria: {
          type: 'score',
          target_value: 240,
          current_value: 0,
          unit: 'points',
          measurement_method: 'USMLE Step 1 score'
        },
        user_id: users[0].id,
        learning_path_id: learningPaths.length > 0 ? learningPaths[0].id : null
      },
      {
        title: 'Study 3 Hours Daily',
        description: 'Maintain consistent daily study schedule of 3 hours minimum',
        type: 'daily',
        category: 'study_time',
        priority: 'medium',
        target_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 1 month from now
        target_criteria: {
          type: 'numeric',
          target_value: 3,
          current_value: 0,
          unit: 'hours',
          measurement_method: 'Daily study time tracking'
        },
        user_id: users[0].id,
        learning_path_id: null
      },
      {
        title: 'Master Clinical Skills Assessment',
        description: 'Achieve 90% proficiency in clinical skills evaluation',
        type: 'monthly',
        category: 'skill_mastery',
        priority: 'high',
        target_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 3 months from now
        target_criteria: {
          type: 'percentage',
          target_value: 90,
          current_value: 0,
          unit: 'percent',
          measurement_method: 'Clinical skills assessment scores'
        },
        user_id: users.length > 1 ? users[1].id : users[0].id,
        learning_path_id: learningPaths.length > 1 ? learningPaths[1].id : null
      }
    ];

    // Insert goals using raw SQL
    for (const goal of goals) {
      await dataSource.query(`
        INSERT INTO learning_goals (
          id, title, description, type, category, priority, target_date,
          target_criteria, status, progress_percentage, streak_count,
          analytics, user_id, learning_path_id, created_at, updated_at
        ) VALUES (
          gen_random_uuid(),
          $1, $2, $3, $4, $5, $6, $7,
          'active',
          0,
          0,
          '{"time_spent_minutes": 0, "sessions_completed": 0, "average_session_duration": 0, "consistency_score": 0, "velocity_trend": "stable"}',
          $8,
          $9,
          NOW(),
          NOW()
        )
        ON CONFLICT DO NOTHING;
      `, [
        goal.title,
        goal.description,
        goal.type,
        goal.category,
        goal.priority,
        goal.target_date,
        JSON.stringify(goal.target_criteria),
        goal.user_id,
        goal.learning_path_id
      ]);
    }

    console.log('✅ Learning goals seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding learning goals:', error);
    throw error;
  }
}
