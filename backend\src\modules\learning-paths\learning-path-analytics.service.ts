import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LearningPath } from '../../entities/learning-path.entity';
import { LearningPathProgress } from '../../entities/learning-path-progress.entity';
import { LearningPathMilestone } from '../../entities/learning-path-milestone.entity';
import { User } from '../../entities/user.entity';

interface PathAnalytics {
  path_id: string;
  title: string;
  total_enrollments: number;
  active_enrollments: number;
  completed_enrollments: number;
  completion_rate: number;
  average_completion_time_days: number;
  average_progress_percentage: number;
  dropout_rate: number;
  user_satisfaction: {
    average_rating: number;
    total_ratings: number;
    rating_distribution: { [key: number]: number };
  };
  engagement_metrics: {
    average_session_duration: number;
    sessions_per_week: number;
    streak_data: {
      average_streak: number;
      max_streak: number;
    };
  };
  milestone_analytics: {
    milestone_id: string;
    title: string;
    achievement_rate: number;
    average_time_to_achieve: number;
  }[];
  difficulty_feedback: {
    too_easy: number;
    just_right: number;
    too_hard: number;
  };
  bottlenecks: {
    phase_id: string;
    phase_title: string;
    average_time_spent: number;
    dropout_rate: number;
  }[];
}

interface UserInsights {
  user_id: string;
  learning_velocity: number;
  preferred_difficulty: string;
  strongest_categories: string[];
  improvement_areas: string[];
  engagement_pattern: {
    most_active_days: string[];
    preferred_session_length: number;
    consistency_score: number;
  };
  goal_achievement_rate: number;
  recommended_next_steps: string[];
  learning_style_indicators: {
    visual_preference: number;
    hands_on_preference: number;
    theoretical_preference: number;
  };
}

interface SystemAnalytics {
  total_paths: number;
  total_enrollments: number;
  overall_completion_rate: number;
  most_popular_categories: { category: string; count: number }[];
  user_engagement_trends: {
    daily_active_users: number;
    weekly_active_users: number;
    monthly_active_users: number;
  };
  content_effectiveness: {
    high_performing_paths: string[];
    underperforming_paths: string[];
  };
  user_journey_insights: {
    common_path_sequences: string[];
    typical_progression_time: number;
  };
}

@Injectable()
export class LearningPathAnalyticsService {
  private readonly logger = new Logger(LearningPathAnalyticsService.name);

  constructor(
    @InjectRepository(LearningPath)
    private pathRepository: Repository<LearningPath>,
    @InjectRepository(LearningPathProgress)
    private progressRepository: Repository<LearningPathProgress>,
    @InjectRepository(LearningPathMilestone)
    private milestoneRepository: Repository<LearningPathMilestone>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  /**
   * Get comprehensive analytics for a specific learning path
   */
  async getPathAnalytics(pathId: string): Promise<PathAnalytics> {
    try {
      const path = await this.pathRepository.findOne({
        where: { id: pathId },
        relations: ['milestones'],
      });

      if (!path) {
        throw new Error('Learning path not found');
      }

      const progressRecords = await this.progressRepository.find({
        where: { learning_path_id: pathId },
        relations: ['user'],
      });

      const totalEnrollments = progressRecords.length;
      const activeEnrollments = progressRecords.filter(p => p.status === 'in_progress').length;
      const completedEnrollments = progressRecords.filter(p => p.status === 'completed').length;
      const completionRate = totalEnrollments > 0 ? (completedEnrollments / totalEnrollments) * 100 : 0;

      // Calculate average completion time
      const completedRecords = progressRecords.filter(p => p.status === 'completed' && p.started_at && p.completed_at);
      const avgCompletionTime = completedRecords.length > 0
        ? completedRecords.reduce((sum, record) => {
            const days = (new Date(record.completed_at!).getTime() - new Date(record.started_at!).getTime()) / (1000 * 60 * 60 * 24);
            return sum + days;
          }, 0) / completedRecords.length
        : 0;

      // Calculate average progress
      const avgProgress = progressRecords.length > 0
        ? progressRecords.reduce((sum, record) => sum + record.overall_progress_percentage, 0) / progressRecords.length
        : 0;

      // Calculate dropout rate (users who haven't accessed in 30+ days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const droppedOut = progressRecords.filter(p => 
        p.status === 'in_progress' && 
        p.last_accessed_at && 
        new Date(p.last_accessed_at) < thirtyDaysAgo
      ).length;
      const dropoutRate = totalEnrollments > 0 ? (droppedOut / totalEnrollments) * 100 : 0;

      // Engagement metrics
      const engagementMetrics = this.calculateEngagementMetrics(progressRecords);

      // Milestone analytics
      const milestoneAnalytics = await this.calculateMilestoneAnalytics(path.milestones, progressRecords);

      // Identify bottlenecks
      const bottlenecks = this.identifyBottlenecks(progressRecords, path);

      return {
        path_id: pathId,
        title: path.title,
        total_enrollments: totalEnrollments,
        active_enrollments: activeEnrollments,
        completed_enrollments: completedEnrollments,
        completion_rate: completionRate,
        average_completion_time_days: avgCompletionTime,
        average_progress_percentage: avgProgress,
        dropout_rate: dropoutRate,
        user_satisfaction: {
          average_rating: path.analytics?.user_ratings?.average || 0,
          total_ratings: path.analytics?.user_ratings?.count || 0,
          rating_distribution: {}, // Would be populated from user feedback
        },
        engagement_metrics: engagementMetrics,
        milestone_analytics: milestoneAnalytics,
        difficulty_feedback: path.analytics?.difficulty_feedback || {
          too_easy: 0,
          just_right: 0,
          too_hard: 0,
        },
        bottlenecks: bottlenecks,
      };
    } catch (error) {
      this.logger.error(`Error getting path analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get personalized insights for a user
   */
  async getUserInsights(userId: string): Promise<UserInsights> {
    try {
      const userProgress = await this.progressRepository.find({
        where: { user_id: userId },
        relations: ['learning_path'],
      });

      if (userProgress.length === 0) {
        return this.getDefaultUserInsights(userId);
      }

      // Calculate learning velocity
      const learningVelocity = this.calculateLearningVelocity(userProgress);

      // Determine preferred difficulty
      const preferredDifficulty = this.determinePreferredDifficulty(userProgress);

      // Identify strongest categories
      const strongestCategories = this.identifyStrongestCategories(userProgress);

      // Identify improvement areas
      const improvementAreas = this.identifyImprovementAreas(userProgress);

      // Analyze engagement patterns
      const engagementPattern = this.analyzeEngagementPattern(userProgress);

      // Calculate goal achievement rate (would need to integrate with goals service)
      const goalAchievementRate = 0.75; // Placeholder

      // Generate recommended next steps
      const recommendedNextSteps = this.generateRecommendedNextSteps(userProgress);

      // Analyze learning style indicators
      const learningStyleIndicators = this.analyzeLearningStyleIndicators(userProgress);

      return {
        user_id: userId,
        learning_velocity: learningVelocity,
        preferred_difficulty: preferredDifficulty,
        strongest_categories: strongestCategories,
        improvement_areas: improvementAreas,
        engagement_pattern: engagementPattern,
        goal_achievement_rate: goalAchievementRate,
        recommended_next_steps: recommendedNextSteps,
        learning_style_indicators: learningStyleIndicators,
      };
    } catch (error) {
      this.logger.error(`Error getting user insights: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get system-wide analytics
   */
  async getSystemAnalytics(): Promise<SystemAnalytics> {
    try {
      const totalPaths = await this.pathRepository.count({
        where: { status: 'published' },
      });

      const totalEnrollments = await this.progressRepository.count();

      const completedEnrollments = await this.progressRepository.count({
        where: { status: 'completed' },
      });

      const overallCompletionRate = totalEnrollments > 0 
        ? (completedEnrollments / totalEnrollments) * 100 
        : 0;

      // Most popular categories
      const categoryStats = await this.progressRepository
        .createQueryBuilder('progress')
        .leftJoin('progress.learning_path', 'path')
        .select('path.category', 'category')
        .addSelect('COUNT(*)', 'count')
        .groupBy('path.category')
        .orderBy('COUNT(*)', 'DESC')
        .limit(5)
        .getRawMany();

      const mostPopularCategories = categoryStats.map(stat => ({
        category: stat.category,
        count: parseInt(stat.count),
      }));

      // User engagement trends
      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const dailyActiveUsers = await this.progressRepository.count({
        where: {
          last_accessed_at: new Date(oneDayAgo.toISOString()),
        },
      });

      const weeklyActiveUsers = await this.progressRepository.count({
        where: {
          last_accessed_at: new Date(oneWeekAgo.toISOString()),
        },
      });

      const monthlyActiveUsers = await this.progressRepository.count({
        where: {
          last_accessed_at: new Date(oneMonthAgo.toISOString()),
        },
      });

      // Content effectiveness
      const pathPerformance = await this.progressRepository
        .createQueryBuilder('progress')
        .leftJoin('progress.learning_path', 'path')
        .select('path.id', 'path_id')
        .addSelect('path.title', 'title')
        .addSelect('AVG(progress.overall_progress_percentage)', 'avg_progress')
        .addSelect('COUNT(CASE WHEN progress.status = \'completed\' THEN 1 END)', 'completions')
        .addSelect('COUNT(*)', 'total_enrollments')
        .groupBy('path.id, path.title')
        .having('COUNT(*) >= 5') // Only paths with at least 5 enrollments
        .getRawMany();

      const highPerforming = pathPerformance
        .filter(p => parseFloat(p.avg_progress) >= 80)
        .map(p => p.path_id)
        .slice(0, 5);

      const underperforming = pathPerformance
        .filter(p => parseFloat(p.avg_progress) < 50)
        .map(p => p.path_id)
        .slice(0, 5);

      return {
        total_paths: totalPaths,
        total_enrollments: totalEnrollments,
        overall_completion_rate: overallCompletionRate,
        most_popular_categories: mostPopularCategories,
        user_engagement_trends: {
          daily_active_users: dailyActiveUsers,
          weekly_active_users: weeklyActiveUsers,
          monthly_active_users: monthlyActiveUsers,
        },
        content_effectiveness: {
          high_performing_paths: highPerforming,
          underperforming_paths: underperforming,
        },
        user_journey_insights: {
          common_path_sequences: [], // Would require more complex analysis
          typical_progression_time: 12, // Placeholder - weeks
        },
      };
    } catch (error) {
      this.logger.error(`Error getting system analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Helper methods
  private calculateEngagementMetrics(progressRecords: LearningPathProgress[]) {
    const totalSessions = progressRecords.reduce((sum, record) => 
      sum + (record.analytics?.study_sessions?.length || 0), 0
    );

    const totalSessionTime = progressRecords.reduce((sum, record) => 
      sum + (record.analytics?.study_sessions?.reduce((s, session) => s + session.duration_minutes, 0) || 0), 0
    );

    const avgSessionDuration = totalSessions > 0 ? totalSessionTime / totalSessions : 0;

    const avgStreak = progressRecords.length > 0
      ? progressRecords.reduce((sum, record) => sum + record.streak_days, 0) / progressRecords.length
      : 0;

    const maxStreak = progressRecords.length > 0
      ? Math.max(...progressRecords.map(record => record.streak_days))
      : 0;

    return {
      average_session_duration: avgSessionDuration,
      sessions_per_week: totalSessions > 0 ? (totalSessions / progressRecords.length) * 7 : 0,
      streak_data: {
        average_streak: avgStreak,
        max_streak: maxStreak,
      },
    };
  }

  private async calculateMilestoneAnalytics(milestones: LearningPathMilestone[], progressRecords: LearningPathProgress[]) {
    return milestones.map(milestone => {
      const achievements = progressRecords.reduce((count, record) => {
        const achieved = record.milestones_achieved.some(m => m.milestone_id === milestone.id);
        return count + (achieved ? 1 : 0);
      }, 0);

      const achievementRate = progressRecords.length > 0 ? (achievements / progressRecords.length) * 100 : 0;

      // Calculate average time to achieve (simplified)
      const avgTimeToAchieve = 7; // Placeholder - would calculate from actual data

      return {
        milestone_id: milestone.id,
        title: milestone.title,
        achievement_rate: achievementRate,
        average_time_to_achieve: avgTimeToAchieve,
      };
    });
  }

  private identifyBottlenecks(progressRecords: LearningPathProgress[], path: LearningPath) {
    // Simplified bottleneck identification
    return path.path_structure.phases.map(phase => ({
      phase_id: phase.id,
      phase_title: phase.title,
      average_time_spent: phase.estimated_duration_weeks * 7, // Convert to days
      dropout_rate: 10, // Placeholder percentage
    }));
  }

  private calculateLearningVelocity(userProgress: LearningPathProgress[]): number {
    const completedPaths = userProgress.filter(p => p.status === 'completed' && p.started_at && p.completed_at);
    
    if (completedPaths.length === 0) return 1.0;

    const velocities = completedPaths.map(progress => {
      const actualWeeks = (new Date(progress.completed_at!).getTime() - new Date(progress.started_at!).getTime()) / (1000 * 60 * 60 * 24 * 7);
      const estimatedWeeks = progress.learning_path.estimated_duration_weeks;
      return estimatedWeeks / actualWeeks; // > 1 means faster than expected
    });

    return velocities.reduce((sum, v) => sum + v, 0) / velocities.length;
  }

  private determinePreferredDifficulty(userProgress: LearningPathProgress[]): string {
    const completedPaths = userProgress.filter(p => p.status === 'completed');
    if (completedPaths.length === 0) return 'beginner';

    const difficulties = completedPaths.map(p => p.learning_path.difficulty);
    const difficultyCount = difficulties.reduce((acc, diff) => {
      acc[diff] = (acc[diff] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    return Object.entries(difficultyCount).sort((a, b) => b[1] - a[1])[0][0];
  }

  private identifyStrongestCategories(userProgress: LearningPathProgress[]): string[] {
    const categoryPerformance = userProgress.reduce((acc, progress) => {
      const category = progress.learning_path.category;
      if (!acc[category]) {
        acc[category] = { total: 0, sum: 0 };
      }
      acc[category].total += 1;
      acc[category].sum += progress.overall_progress_percentage;
      return acc;
    }, {} as { [key: string]: { total: number; sum: number } });

    return Object.entries(categoryPerformance)
      .map(([category, data]) => ({ category, avg: data.sum / data.total }))
      .sort((a, b) => b.avg - a.avg)
      .slice(0, 3)
      .map(item => item.category);
  }

  private identifyImprovementAreas(userProgress: LearningPathProgress[]): string[] {
    const categoryPerformance = userProgress.reduce((acc, progress) => {
      const category = progress.learning_path.category;
      if (!acc[category]) {
        acc[category] = { total: 0, sum: 0 };
      }
      acc[category].total += 1;
      acc[category].sum += progress.overall_progress_percentage;
      return acc;
    }, {} as { [key: string]: { total: number; sum: number } });

    return Object.entries(categoryPerformance)
      .map(([category, data]) => ({ category, avg: data.sum / data.total }))
      .filter(item => item.avg < 70) // Below 70% average
      .sort((a, b) => a.avg - b.avg)
      .slice(0, 3)
      .map(item => item.category);
  }

  private analyzeEngagementPattern(userProgress: LearningPathProgress[]) {
    // Simplified engagement pattern analysis
    return {
      most_active_days: ['Monday', 'Wednesday', 'Friday'], // Placeholder
      preferred_session_length: 45, // minutes
      consistency_score: 0.8, // 0-1 scale
    };
  }

  private generateRecommendedNextSteps(userProgress: LearningPathProgress[]): string[] {
    const recommendations = [];
    
    const inProgressPaths = userProgress.filter(p => p.status === 'in_progress');
    if (inProgressPaths.length > 0) {
      recommendations.push('Continue your current learning path');
    }

    const completedPaths = userProgress.filter(p => p.status === 'completed');
    if (completedPaths.length >= 2) {
      recommendations.push('Consider advanced level paths');
    }

    if (recommendations.length === 0) {
      recommendations.push('Start with a beginner-friendly learning path');
    }

    return recommendations;
  }

  private analyzeLearningStyleIndicators(userProgress: LearningPathProgress[]) {
    // Simplified learning style analysis
    return {
      visual_preference: 0.7,
      hands_on_preference: 0.6,
      theoretical_preference: 0.5,
    };
  }

  private getDefaultUserInsights(userId: string): UserInsights {
    return {
      user_id: userId,
      learning_velocity: 1.0,
      preferred_difficulty: 'beginner',
      strongest_categories: [],
      improvement_areas: [],
      engagement_pattern: {
        most_active_days: [],
        preferred_session_length: 60,
        consistency_score: 0.5,
      },
      goal_achievement_rate: 0,
      recommended_next_steps: ['Start your first learning path'],
      learning_style_indicators: {
        visual_preference: 0.5,
        hands_on_preference: 0.5,
        theoretical_preference: 0.5,
      },
    };
  }
}
