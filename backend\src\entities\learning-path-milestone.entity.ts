import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { LearningPath } from './learning-path.entity';

export enum MilestoneType {
  PHASE_COMPLETION = 'phase_completion',
  ASSESSMENT_SCORE = 'assessment_score',
  TIME_BASED = 'time_based',
  SKILL_MASTERY = 'skill_mastery',
  STREAK_ACHIEVEMENT = 'streak_achievement',
  CUSTOM = 'custom',
}

export enum MilestoneStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
}

@Entity('learning_path_milestones')
export class LearningPathMilestone {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  learning_path_id: string;

  @Column({ type: 'varchar', length: 255 })
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({
    type: 'enum',
    enum: MilestoneType,
    default: MilestoneType.CUSTOM,
  })
  type: MilestoneType;

  @Column({
    type: 'enum',
    enum: MilestoneStatus,
    default: MilestoneStatus.ACTIVE,
  })
  status: MilestoneStatus;

  @Column({ type: 'int', default: 0 })
  order: number;

  @Column({ type: 'jsonb' })
  criteria: {
    type: 'phase_completion' | 'assessment_score' | 'time_based' | 'skill_mastery' | 'streak' | 'custom';
    conditions: {
      phase_id?: string;
      assessment_id?: string;
      minimum_score?: number;
      time_period_days?: number;
      skill_name?: string;
      mastery_level?: number;
      streak_days?: number;
      custom_logic?: string;
    };
    required_modules?: string[];
    optional_modules?: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  rewards: {
    points?: number;
    badge_id?: string;
    certificate?: {
      template_id: string;
      title: string;
      description: string;
    };
    unlock_content?: {
      courses?: string[];
      assessments?: string[];
      clinical_cases?: string[];
    };
    custom_rewards?: {
      type: string;
      value: any;
    }[];
  };

  @Column({ type: 'varchar', length: 500, nullable: true })
  icon_url: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  color: string;

  @Column({ type: 'boolean', default: true })
  is_required: boolean;

  @Column({ type: 'boolean', default: true })
  is_visible: boolean;

  @Column({ type: 'jsonb', nullable: true })
  celebration_config: {
    show_animation: boolean;
    animation_type: 'confetti' | 'fireworks' | 'badge' | 'custom';
    message: string;
    auto_share: boolean;
    notification_settings: {
      email: boolean;
      push: boolean;
      in_app: boolean;
    };
  };

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    total_achieved: number;
    achievement_rate: number;
    average_time_to_achieve_days: number;
    user_feedback: {
      difficulty_rating: number;
      satisfaction_rating: number;
      comments: string[];
    };
  };

  @ManyToOne(() => LearningPath, (path) => path.milestones, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'learning_path_id' })
  learning_path: LearningPath;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
