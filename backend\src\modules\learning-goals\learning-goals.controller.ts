import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { Request as ExpressRequest } from 'express';
import { ApiTags, ApiOperation, ApiResponse, Api<PERSON>earerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { 
  LearningGoalsService, 
  CreateLearningGoalDto, 
  UpdateLearningGoalDto, 
  GoalFilters 
} from './learning-goals.service';
import { ProgressEntryType } from '../../entities/learning-goal-progress.entity';

@ApiTags('learning-goals')
@Controller('learning-goals')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LearningGoalsController {
  constructor(private readonly learningGoalsService: LearningGoalsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new learning goal' })
  @ApiResponse({ status: 201, description: 'Learning goal created successfully' })
  async create(@Body() createDto: CreateLearningGoalDto, @Request() req: ExpressRequest & { user: any }) {
    return await this.learningGoalsService.create(createDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all learning goals for the user' })
  @ApiResponse({ status: 200, description: 'Learning goals retrieved successfully' })
  async findAll(@Request() req: ExpressRequest & { user: any }, @Query() filters: GoalFilters) {
    return await this.learningGoalsService.findAll(req.user.id, filters);
  }

  @Get('analytics')
  @ApiOperation({ summary: 'Get learning goals analytics' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  async getAnalytics(@Request() req) {
    return await this.learningGoalsService.getAnalytics(req.user.id);
  }

  @Post('smart-suggestions')
  @ApiOperation({ summary: 'Get SMART criteria suggestions for a goal' })
  @ApiResponse({ status: 200, description: 'SMART suggestions generated successfully' })
  async getSMARTSuggestions(@Body() goalData: Partial<CreateLearningGoalDto>) {
    return await this.learningGoalsService.generateSMARTSuggestions(goalData);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a learning goal by ID' })
  @ApiResponse({ status: 200, description: 'Learning goal retrieved successfully' })
  async findOne(@Param('id') id: string, @Request() req) {
    return await this.learningGoalsService.findOne(id, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a learning goal' })
  @ApiResponse({ status: 200, description: 'Learning goal updated successfully' })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateLearningGoalDto,
    @Request() req,
  ) {
    return await this.learningGoalsService.update(id, updateDto, req.user.id);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a learning goal' })
  @ApiResponse({ status: 204, description: 'Learning goal deleted successfully' })
  async remove(@Param('id') id: string, @Request() req) {
    await this.learningGoalsService.remove(id, req.user.id);
  }

  @Post(':id/progress')
  @ApiOperation({ summary: 'Add progress entry to a learning goal' })
  @ApiResponse({ status: 201, description: 'Progress entry added successfully' })
  async addProgress(
    @Param('id') id: string,
    @Body() progressData: {
      progress_value: number;
      notes?: string;
      entry_type?: ProgressEntryType;
      metadata?: any;
      context?: any;
    },
    @Request() req,
  ) {
    return await this.learningGoalsService.addProgressEntry(id, req.user.id, {
      entry_type: progressData.entry_type || ProgressEntryType.MANUAL,
      progress_value: progressData.progress_value,
      notes: progressData.notes,
      metadata: progressData.metadata,
      context: progressData.context,
    });
  }

  @Get(':id/progress')
  @ApiOperation({ summary: 'Get progress history for a learning goal' })
  @ApiResponse({ status: 200, description: 'Progress history retrieved successfully' })
  async getProgress(@Param('id') id: string, @Request() req) {
    return await this.learningGoalsService.getGoalProgress(id, req.user.id);
  }
}
