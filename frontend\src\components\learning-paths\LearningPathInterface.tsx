'use client';

import React, { useState, useEffect } from 'react';
import { 
  <PERSON>O<PERSON>, 
  Target, 
  Clock, 
  Users, 
  Star, 
  Play, 
  Pause, 
  CheckCircle,
  TrendingUp,
  Award,
  Filter,
  Search,
  Grid,
  List
} from 'lucide-react';

interface LearningPath {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: string;
  status: string;
  estimated_duration_weeks: number;
  estimated_hours_per_week: number;
  tags: string[];
  learning_objectives: string[];
  analytics: {
    total_enrollments: number;
    completion_rate: number;
    user_ratings: {
      average: number;
      count: number;
    };
  };
  created_by: {
    id: string;
    first_name: string;
    last_name: string;
  };
  courses: any[];
  milestones: any[];
}

interface LearningPathProgress {
  id: string;
  status: string;
  overall_progress_percentage: number;
  started_at: string;
  last_accessed_at: string;
  learning_path: LearningPath;
}

export const LearningPathInterface: React.FC = () => {
  const [learningPaths, setLearningPaths] = useState<LearningPath[]>([]);
  const [userProgress, setUserProgress] = useState<LearningPathProgress[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState({
    category: '',
    difficulty: '',
    status: '',
    search: '',
  });
  const [activeTab, setActiveTab] = useState<'browse' | 'my-paths' | 'recommendations'>('browse');

  useEffect(() => {
    fetchLearningPaths();
    fetchUserProgress();
  }, [filters]);

  const fetchLearningPaths = async () => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });

      const response = await fetch(`/api/learning-paths?${queryParams}`);
      const data = await response.json();
      setLearningPaths(data);
    } catch (error) {
      console.error('Error fetching learning paths:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchUserProgress = async () => {
    try {
      const response = await fetch('/api/learning-paths/my-progress');
      const data = await response.json();
      setUserProgress(data);
    } catch (error) {
      console.error('Error fetching user progress:', error);
    }
  };

  const enrollInPath = async (pathId: string) => {
    try {
      const response = await fetch(`/api/learning-paths/${pathId}/enroll`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          preferences: {
            daily_study_goal_minutes: 60,
            reminder_settings: { enabled: true, frequency: 'daily', time: '09:00' },
          },
        }),
      });

      if (response.ok) {
        fetchUserProgress();
        // Show success notification
      }
    } catch (error) {
      console.error('Error enrolling in path:', error);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-orange-100 text-orange-800';
      case 'expert': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'not_started': return 'bg-gray-100 text-gray-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderPathCard = (path: LearningPath, progress?: LearningPathProgress) => {
    const isEnrolled = !!progress;
    
    return (
      <div key={path.id} className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{path.title}</h3>
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">{path.description}</p>
              
              <div className="flex flex-wrap gap-2 mb-3">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(path.difficulty)}`}>
                  {path.difficulty}
                </span>
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {path.category.replace('_', ' ')}
                </span>
                {isEnrolled && (
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(progress.status)}`}>
                    {progress.status.replace('_', ' ')}
                  </span>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-1 text-yellow-400">
              <Star className="w-4 h-4 fill-current" />
              <span className="text-sm text-gray-600">
                {path.analytics.user_ratings.average.toFixed(1)} ({path.analytics.user_ratings.count})
              </span>
            </div>
          </div>

          {isEnrolled && (
            <div className="mb-4">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                <span>Progress</span>
                <span>{Math.round(progress.overall_progress_percentage)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress.overall_progress_percentage}%` }}
                />
              </div>
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{path.estimated_duration_weeks}w</span>
              </div>
              <div className="flex items-center space-x-1">
                <BookOpen className="w-4 h-4" />
                <span>{path.courses.length} courses</span>
              </div>
              <div className="flex items-center space-x-1">
                <Users className="w-4 h-4" />
                <span>{path.analytics.total_enrollments}</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-1">
              <TrendingUp className="w-4 h-4" />
              <span>{Math.round(path.analytics.completion_rate)}% completion</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              by {path.created_by.first_name} {path.created_by.last_name}
            </div>
            
            <div className="flex space-x-2">
              {isEnrolled ? (
                <>
                  {progress.status === 'paused' ? (
                    <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-1">
                      <Play className="w-4 h-4" />
                      <span>Resume</span>
                    </button>
                  ) : progress.status === 'completed' ? (
                    <button className="px-4 py-2 bg-gray-600 text-white rounded-lg flex items-center space-x-1">
                      <CheckCircle className="w-4 h-4" />
                      <span>Completed</span>
                    </button>
                  ) : (
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                      Continue
                    </button>
                  )}
                </>
              ) : (
                <button 
                  onClick={() => enrollInPath(path.id)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Enroll
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderFilters = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
      <div className="flex flex-wrap gap-4">
        <div className="flex-1 min-w-64">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search learning paths..."
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <select
          value={filters.category}
          onChange={(e) => setFilters({ ...filters, category: e.target.value })}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Categories</option>
          <option value="usmle_step1">USMLE Step 1</option>
          <option value="usmle_step2">USMLE Step 2</option>
          <option value="clinical_skills">Clinical Skills</option>
          <option value="specialty_prep">Specialty Prep</option>
        </select>
        
        <select
          value={filters.difficulty}
          onChange={(e) => setFilters({ ...filters, difficulty: e.target.value })}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Levels</option>
          <option value="beginner">Beginner</option>
          <option value="intermediate">Intermediate</option>
          <option value="advanced">Advanced</option>
          <option value="expert">Expert</option>
        </select>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'my-paths':
        return (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {userProgress.map(progress => renderPathCard(progress.learning_path, progress))}
          </div>
        );
      
      case 'recommendations':
        // This would fetch recommended paths
        return (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {learningPaths.slice(0, 6).map(path => renderPathCard(path))}
          </div>
        );
      
      default:
        return (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {learningPaths.map(path => {
              const progress = userProgress.find(p => p.learning_path.id === path.id);
              return renderPathCard(path, progress);
            })}
          </div>
        );
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Learning Paths</h1>
        <p className="text-gray-600">
          Structured learning journeys to help you achieve your medical education goals
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'browse', label: 'Browse All', icon: BookOpen },
            { id: 'my-paths', label: 'My Paths', icon: Target },
            { id: 'recommendations', label: 'Recommended', icon: Award },
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {renderFilters()}

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        renderTabContent()
      )}
    </div>
  );
};
