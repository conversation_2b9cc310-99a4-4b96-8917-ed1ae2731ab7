import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, ILike } from 'typeorm';
import {
  Course,
  CourseDifficulty,
  CourseStatus,
} from '../../entities/course.entity';
import { CourseCategory } from '../../entities/course-category.entity';
import {
  CourseEnrollment,
  EnrollmentStatus,
} from '../../entities/course-enrollment.entity';
import { User } from '../../entities/user.entity';
import { Unit } from '../../entities/unit.entity';

export interface CreateCourseDto {
  title: string;
  description: string;
  short_description?: string;
  code: string;
  difficulty: CourseDifficulty;
  estimated_hours: number;
  credit_hours?: number;
  category_id?: string;
  tags?: string[];
  learning_objectives?: string[];
  prerequisites?: {
    course_ids?: string[];
    skills?: string[];
    description?: string;
  };
  is_public?: boolean;
  price?: number;
}

export interface UpdateCourseDto extends Partial<CreateCourseDto> {
  status?: CourseStatus;
  is_featured?: boolean;
}

export interface CourseSearchFilters {
  search?: string;
  category_id?: string;
  difficulty?: CourseDifficulty;
  status?: CourseStatus;
  is_featured?: boolean;
  instructor_id?: string;
  tags?: string[];
  min_rating?: number;
  max_price?: number;
  page?: number;
  limit?: number;
  sort_by?:
    | 'title'
    | 'rating'
    | 'enrollment_count'
    | 'created_at'
    | 'updated_at';
  sort_order?: 'ASC' | 'DESC';
}

@Injectable()
export class CoursesService {
  constructor(
    @InjectRepository(Course)
    private readonly courseRepository: Repository<Course>,
    @InjectRepository(CourseCategory)
    private readonly categoryRepository: Repository<CourseCategory>,
    @InjectRepository(CourseEnrollment)
    private readonly enrollmentRepository: Repository<CourseEnrollment>,
    @InjectRepository(Unit)
    private readonly unitRepository: Repository<Unit>,
  ) {}

  async create(
    createCourseDto: CreateCourseDto,
    instructorId: string,
  ): Promise<Course> {
    // Check if course code already exists
    const existingCourse = await this.courseRepository.findOne({
      where: { code: createCourseDto.code },
    });

    if (existingCourse) {
      throw new BadRequestException('Course code already exists');
    }

    // Validate category if provided
    if (createCourseDto.category_id) {
      const category = await this.categoryRepository.findOne({
        where: { id: createCourseDto.category_id },
      });
      if (!category) {
        throw new NotFoundException('Category not found');
      }
    }

    const course = this.courseRepository.create({
      ...createCourseDto,
      instructor_id: instructorId,
      status: CourseStatus.DRAFT,
    });

    return await this.courseRepository.save(course);
  }

  async findAll(filters: CourseSearchFilters = {}) {
    const {
      search,
      category_id,
      difficulty,
      status = CourseStatus.PUBLISHED,
      is_featured,
      instructor_id,
      tags,
      min_rating,
      max_price,
      page = 1,
      limit = 20,
      sort_by = 'created_at',
      sort_order = 'DESC',
    } = filters;

    const queryBuilder = this.courseRepository
      .createQueryBuilder('course')
      .leftJoinAndSelect('course.instructor', 'instructor')
      .leftJoinAndSelect('course.category', 'category')
      .leftJoinAndSelect('course.units', 'units');

    // Apply filters
    if (search) {
      queryBuilder.andWhere(
        '(course.title ILIKE :search OR course.description ILIKE :search OR course.tags && ARRAY[:search])',
        { search: `%${search}%` },
      );
    }

    if (category_id) {
      queryBuilder.andWhere('course.category.id = :category_id', {
        category_id,
      });
    }

    if (difficulty) {
      queryBuilder.andWhere('course.difficulty = :difficulty', { difficulty });
    }

    if (status) {
      queryBuilder.andWhere('course.status = :status', { status });
    }

    if (is_featured !== undefined) {
      queryBuilder.andWhere('course.is_featured = :is_featured', {
        is_featured,
      });
    }

    if (instructor_id) {
      queryBuilder.andWhere('course.instructor_id = :instructor_id', {
        instructor_id,
      });
    }

    if (tags && tags.length > 0) {
      queryBuilder.andWhere('course.tags && :tags', { tags });
    }

    if (min_rating) {
      queryBuilder.andWhere('course.rating >= :min_rating', { min_rating });
    }

    if (max_price) {
      queryBuilder.andWhere(
        '(course.price IS NULL OR course.price <= :max_price)',
        { max_price },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`course.${sort_by}`, sort_order);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [courses, total] = await queryBuilder.getManyAndCount();

    return {
      courses,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, userId?: string): Promise<Course> {
    const course = await this.courseRepository.findOne({
      where: { id },
      relations: [
        'instructor',
        'category',
        'units',
        'units.materials',
        'prerequisite_courses',
      ],
    });

    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // If user is provided, check enrollment status
    if (userId) {
      const enrollment = await this.enrollmentRepository.findOne({
        where: { user_id: userId, course_id: id },
      });

      (course as any).enrollment = enrollment;
      (course as any).is_enrolled = !!enrollment;
    }

    return course;
  }

  async update(
    id: string,
    updateCourseDto: UpdateCourseDto,
    userId: string,
  ): Promise<Course> {
    const course = await this.courseRepository.findOne({
      where: { id },
      relations: ['instructor'],
    });

    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Check if user is the instructor
    if (course.instructor_id !== userId) {
      throw new ForbiddenException(
        'Only the instructor can update this course',
      );
    }

    // Validate category if being updated
    if (updateCourseDto.category_id) {
      const category = await this.categoryRepository.findOne({
        where: { id: updateCourseDto.category_id },
      });
      if (!category) {
        throw new NotFoundException('Category not found');
      }
    }

    Object.assign(course, updateCourseDto);
    return await this.courseRepository.save(course);
  }

  async remove(id: string, userId: string): Promise<void> {
    const course = await this.courseRepository.findOne({
      where: { id },
      relations: ['instructor'],
    });

    if (!course) {
      throw new NotFoundException('Course not found');
    }

    // Check if user is the instructor
    if (course.instructor_id !== userId) {
      throw new ForbiddenException(
        'Only the instructor can delete this course',
      );
    }

    await this.courseRepository.remove(course);
  }

  async enroll(courseId: string, userId: string): Promise<CourseEnrollment> {
    // Check if course exists and is published
    const course = await this.courseRepository.findOne({
      where: { id: courseId, status: CourseStatus.PUBLISHED },
      relations: ['units', 'prerequisite_courses'],
    });

    if (!course) {
      throw new NotFoundException(
        'Course not found or not available for enrollment',
      );
    }

    // Check if user is already enrolled
    const existingEnrollment = await this.enrollmentRepository.findOne({
      where: { user_id: userId, course_id: courseId },
    });

    if (existingEnrollment) {
      throw new BadRequestException('User is already enrolled in this course');
    }

    // Check prerequisites
    await this.checkPrerequisites(course, userId);

    // Create enrollment
    const enrollment = this.enrollmentRepository.create({
      user_id: userId,
      course_id: courseId,
      total_units: course.units.length,
      status: EnrollmentStatus.ACTIVE,
    });

    const savedEnrollment = await this.enrollmentRepository.save(enrollment);

    // Update course enrollment count
    await this.courseRepository.update(courseId, {
      enrollment_count: course.enrollment_count + 1,
    });

    return savedEnrollment;
  }

  async unenroll(courseId: string, userId: string): Promise<void> {
    const enrollment = await this.enrollmentRepository.findOne({
      where: { user_id: userId, course_id: courseId },
    });

    if (!enrollment) {
      throw new NotFoundException('Enrollment not found');
    }

    await this.enrollmentRepository.update(enrollment.id, {
      status: EnrollmentStatus.DROPPED,
    });

    // Update course enrollment count
    const course = await this.courseRepository.findOne({
      where: { id: courseId },
    });
    if (course) {
      await this.courseRepository.update(courseId, {
        enrollment_count: Math.max(0, course.enrollment_count - 1),
      });
    }
  }

  async getEnrollments(userId: string, status?: EnrollmentStatus) {
    const where: FindOptionsWhere<CourseEnrollment> = { user_id: userId };
    if (status) {
      where.status = status;
    }

    return await this.enrollmentRepository.find({
      where,
      relations: ['course', 'course.instructor', 'course.category'],
      order: { enrolled_at: 'DESC' },
    });
  }

  async updateProgress(
    courseId: string,
    userId: string,
    unitId: string,
  ): Promise<void> {
    const enrollment = await this.enrollmentRepository.findOne({
      where: { user_id: userId, course_id: courseId },
    });

    if (!enrollment) {
      throw new NotFoundException('Enrollment not found');
    }

    // Update completed units count and progress percentage
    const totalUnits = await this.unitRepository.count({
      where: { course_id: courseId },
    });

    const completedUnits = enrollment.completed_units + 1;
    const progressPercentage = (completedUnits / totalUnits) * 100;

    await this.enrollmentRepository.update(enrollment.id, {
      completed_units: completedUnits,
      progress_percentage: progressPercentage,
      last_accessed: new Date(),
      ...(progressPercentage >= 100 && {
        status: EnrollmentStatus.COMPLETED,
        completed_at: new Date(),
      }),
    });
  }

  async getFeaturedCourses(limit = 10) {
    return await this.courseRepository.find({
      where: {
        status: CourseStatus.PUBLISHED,
        is_featured: true
      },
      relations: ['instructor', 'category'],
      order: { rating: 'DESC', enrollment_count: 'DESC' },
      take: limit,
    });
  }

  async getRecommendedCourses(userId: string, limit = 10) {
    // Get user's enrolled courses to understand their interests
    const userEnrollments = await this.enrollmentRepository.find({
      where: { user_id: userId },
      relations: ['course', 'course.category'],
    });

    const enrolledCategoryIds = userEnrollments
      .map(e => e.course.category?.id)
      .filter(Boolean);

    const enrolledCourseIds = userEnrollments.map(e => e.course.id);

    // Find courses in similar categories that user hasn't enrolled in
    const queryBuilder = this.courseRepository
      .createQueryBuilder('course')
      .leftJoinAndSelect('course.instructor', 'instructor')
      .leftJoinAndSelect('course.category', 'category')
      .where('course.status = :status', { status: CourseStatus.PUBLISHED })
      .andWhere('course.id NOT IN (:...enrolledIds)', {
        enrolledIds: enrolledCourseIds.length > 0 ? enrolledCourseIds : ['']
      });

    if (enrolledCategoryIds.length > 0) {
      queryBuilder.andWhere('course.category_id IN (:...categoryIds)', {
        categoryIds: enrolledCategoryIds
      });
    }

    return await queryBuilder
      .orderBy('course.rating', 'DESC')
      .addOrderBy('course.enrollment_count', 'DESC')
      .take(limit)
      .getMany();
  }

  private async checkPrerequisites(course: Course, userId: string): Promise<void> {
    if (!course.prerequisites) {
      return;
    }

    // Check course prerequisites
    if (course.prerequisites.course_ids && course.prerequisites.course_ids.length > 0) {
      const completedCourses = await this.enrollmentRepository.find({
        where: {
          user_id: userId,
          status: EnrollmentStatus.COMPLETED,
        },
        relations: ['course'],
      });

      const completedCourseIds = completedCourses.map(e => e.course_id);
      const missingPrerequisites = course.prerequisites.course_ids.filter(
        id => !completedCourseIds.includes(id)
      );

      if (missingPrerequisites.length > 0) {
        const prerequisiteCourses = await this.courseRepository.findByIds(missingPrerequisites);

        throw new BadRequestException(
          `Prerequisites not met. Please complete: ${prerequisiteCourses.map(c => c.title).join(', ')}`
        );
      }
    }
  }
}
