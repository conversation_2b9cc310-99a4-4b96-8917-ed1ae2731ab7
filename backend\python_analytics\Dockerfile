# Build stage
FROM python:3.11-slim AS builder

WORKDIR /app

# Install security updates and system dependencies for building
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    --no-install-recommends \
    build-essential \
    gcc \
    g++ \
    python3-dev \
    libhdf5-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && apt-get autoremove -y

# Copy requirements file
COPY requirements.txt .

# Create virtual environment and install dependencies with security checks
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir --upgrade pip==24.3.1 setuptools==75.6.0 wheel==0.45.1 && \
    pip install --no-cache-dir --no-compile -r requirements.txt && \
    pip check && \
    find /opt/venv -name "*.pyc" -delete && \
    find /opt/venv -name "__pycache__" -type d -exec rm -rf {} + || true

# Production stage
FROM python:3.11-slim AS runner

WORKDIR /app

# Install security updates and essential runtime dependencies
RUN apt-get update && apt-get upgrade -y && apt-get install -y \
    --no-install-recommends \
    curl \
    tini \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    && apt-get autoremove -y \
    && rm -rf /var/cache/apt/*

# Create non-root user for security with restricted permissions
RUN groupadd --gid 1001 --system python \
    && useradd --uid 1001 --gid python --system --shell /bin/false --no-create-home python

# Copy virtual environment from builder with proper ownership
COPY --from=builder --chown=python:python /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy source code with proper ownership and minimal permissions
COPY --chown=python:python --chmod=644 main.py ./
COPY --chown=python:python --chmod=644 analytics/ ./analytics/
COPY --chown=python:python --chmod=644 auth/ ./auth/
COPY --chown=python:python --chmod=644 config.py ./
COPY --chown=python:python --chmod=644 __init__.py ./

# Create scripts directory and copy with proper permissions
RUN mkdir -p ./scripts
COPY --chown=python:python --chmod=755 scripts/ ./scripts/

# Create necessary directories with restricted permissions
RUN mkdir -p /app/models /app/logs /tmp/app && \
    chown -R python:python /app/models /app/logs /tmp/app && \
    chmod 750 /app/models /app/logs /tmp/app

# Switch to non-root user
USER python

# Set secure environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONHASHSEED=random \
    PORT=5000 \
    PYTHONPATH=/app \
    PYTHONOPTIMIZE=1 \
    TMPDIR=/tmp/app

# Add health check with timeout
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:${PORT}/health || exit 1

# Expose the port
EXPOSE 5000

# Use tini as init system for proper signal handling
ENTRYPOINT ["tini", "--"]

# Start the application with security optimizations
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "5000", "--workers", "1", "--access-log", "--no-server-header"]