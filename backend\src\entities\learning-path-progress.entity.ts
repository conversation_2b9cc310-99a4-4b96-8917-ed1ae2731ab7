import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Unique,
} from 'typeorm';
import { User } from './user.entity';
import { LearningPath } from './learning-path.entity';

export enum LearningPathProgressStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  PAUSED = 'paused',
  DROPPED = 'dropped',
}

@Entity('learning_path_progress')
@Unique(['user_id', 'learning_path_id'])
export class LearningPathProgress {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  user_id: string;

  @Column({ type: 'uuid' })
  learning_path_id: string;

  @Column({
    type: 'enum',
    enum: LearningPathProgressStatus,
    default: LearningPathProgressStatus.NOT_STARTED,
  })
  status: LearningPathProgressStatus;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  overall_progress_percentage: number;

  @Column({ type: 'int', default: 0 })
  current_phase_index: number;

  @Column({ type: 'int', default: 0 })
  current_module_index: number;

  @Column({ type: 'jsonb', nullable: true })
  phase_progress: {
    phase_id: string;
    status: 'not_started' | 'in_progress' | 'completed';
    progress_percentage: number;
    started_at?: Date;
    completed_at?: Date;
    modules_completed: string[];
    current_module_id?: string;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  module_progress: {
    module_id: string;
    phase_id: string;
    status: 'not_started' | 'in_progress' | 'completed' | 'skipped';
    progress_percentage: number;
    started_at?: Date;
    completed_at?: Date;
    time_spent_minutes: number;
    attempts: number;
    best_score?: number;
    notes?: string;
  }[];

  @Column({ type: 'jsonb', nullable: true })
  milestones_achieved: {
    milestone_id: string;
    achieved_at: Date;
    score?: number;
    notes?: string;
  }[];

  @Column({ type: 'int', default: 0 })
  total_time_spent_minutes: number;

  @Column({ type: 'timestamp', nullable: true })
  started_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  completed_at: Date;

  @Column({ type: 'timestamp', nullable: true })
  last_accessed_at: Date;

  @Column({ type: 'int', default: 0 })
  streak_days: number;

  @Column({ type: 'timestamp', nullable: true })
  last_activity_date: Date;

  @Column({ type: 'jsonb', nullable: true })
  preferences: {
    daily_study_goal_minutes?: number;
    preferred_study_times?: string[];
    reminder_settings?: {
      enabled: boolean;
      frequency: 'daily' | 'weekly' | 'custom';
      time: string;
    };
    difficulty_preference?: 'adaptive' | 'fixed';
  };

  @Column({ type: 'jsonb', nullable: true })
  analytics: {
    study_sessions: {
      date: Date;
      duration_minutes: number;
      modules_covered: string[];
      performance_score?: number;
    }[];
    performance_trends: {
      week: number;
      average_score: number;
      time_spent_minutes: number;
      modules_completed: number;
    }[];
    learning_velocity: {
      modules_per_week: number;
      hours_per_week: number;
      consistency_score: number;
    };
  };

  @Column({ type: 'text', nullable: true })
  notes: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @ManyToOne(() => LearningPath, (path) => path.user_progress, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'learning_path_id' })
  learning_path: LearningPath;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;
}
