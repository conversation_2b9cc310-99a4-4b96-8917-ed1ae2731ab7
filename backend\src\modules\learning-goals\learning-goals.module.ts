import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LearningGoalsService } from './learning-goals.service';
import { LearningGoalsController } from './learning-goals.controller';
import { LearningGoal } from '../../entities/learning-goal.entity';
import { LearningGoalProgress } from '../../entities/learning-goal-progress.entity';
import { User } from '../../entities/user.entity';
import { LearningPath } from '../../entities/learning-path.entity';
import { Course } from '../../entities/course.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      LearningGoal,
      LearningGoalProgress,
      User,
      LearningPath,
      Course,
    ]),
  ],
  controllers: [LearningGoalsController],
  providers: [LearningGoalsService],
  exports: [LearningGoalsService],
})
export class LearningGoalsModule {}
