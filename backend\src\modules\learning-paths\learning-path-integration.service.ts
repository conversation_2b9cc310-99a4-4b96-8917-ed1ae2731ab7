import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LearningPathProgress } from '../../entities/learning-path-progress.entity';
import { LearningGoal } from '../../entities/learning-goal.entity';
import { Progress } from '../../entities/progress.entity';
import { AssessmentAttempt } from '../../entities/assessment-attempt.entity';
import { ClinicalCaseAttempt } from '../../entities/clinical-case-attempt.entity';
import { CourseEnrollment } from '../../entities/course-enrollment.entity';
import { LearningPathProgressService } from './learning-path-progress.service';
import { LearningGoalsService } from '../learning-goals/learning-goals.service';

@Injectable()
export class LearningPathIntegrationService {
  private readonly logger = new Logger(LearningPathIntegrationService.name);

  constructor(
    @InjectRepository(LearningPathProgress)
    private pathProgressRepository: Repository<LearningPathProgress>,
    @InjectRepository(LearningGoal)
    private goalRepository: Repository<LearningGoal>,
    @InjectRepository(Progress)
    private materialProgressRepository: Repository<Progress>,
    @InjectRepository(AssessmentAttempt)
    private assessmentAttemptRepository: Repository<AssessmentAttempt>,
    @InjectRepository(ClinicalCaseAttempt)
    private clinicalCaseAttemptRepository: Repository<ClinicalCaseAttempt>,
    @InjectRepository(CourseEnrollment)
    private courseEnrollmentRepository: Repository<CourseEnrollment>,
    private pathProgressService: LearningPathProgressService,
    private goalsService: LearningGoalsService,
  ) {}

  /**
   * Sync course progress with learning path progress
   */
  async syncCourseProgress(userId: string, courseId: string, progressData: any): Promise<void> {
    try {
      // Find learning paths that include this course
      const pathProgresses = await this.pathProgressRepository
        .createQueryBuilder('progress')
        .leftJoinAndSelect('progress.learning_path', 'path')
        .leftJoinAndSelect('path.courses', 'courses')
        .where('progress.user_id = :userId', { userId })
        .andWhere('courses.id = :courseId', { courseId })
        .getMany();

      for (const pathProgress of pathProgresses) {
        await this.updatePathProgressFromCourse(pathProgress, courseId, progressData);
      }

      // Update related goals
      await this.updateGoalsFromCourseProgress(userId, courseId, progressData);
    } catch (error) {
      this.logger.error(`Error syncing course progress: ${error.message}`, error.stack);
    }
  }

  /**
   * Sync assessment results with learning path progress
   */
  async syncAssessmentResults(userId: string, assessmentId: string, attemptData: any): Promise<void> {
    try {
      // Find learning paths that include this assessment
      const pathProgresses = await this.pathProgressRepository
        .createQueryBuilder('progress')
        .leftJoinAndSelect('progress.learning_path', 'path')
        .leftJoinAndSelect('path.assessments', 'assessments')
        .where('progress.user_id = :userId', { userId })
        .andWhere('assessments.id = :assessmentId', { assessmentId })
        .getMany();

      for (const pathProgress of pathProgresses) {
        await this.updatePathProgressFromAssessment(pathProgress, assessmentId, attemptData);
      }

      // Update related goals
      await this.updateGoalsFromAssessmentResults(userId, assessmentId, attemptData);
    } catch (error) {
      this.logger.error(`Error syncing assessment results: ${error.message}`, error.stack);
    }
  }

  /**
   * Sync clinical case completion with learning path progress
   */
  async syncClinicalCaseCompletion(userId: string, caseId: string, attemptData: any): Promise<void> {
    try {
      // Find learning paths that include this clinical case
      const pathProgresses = await this.pathProgressRepository
        .createQueryBuilder('progress')
        .leftJoinAndSelect('progress.learning_path', 'path')
        .leftJoinAndSelect('path.clinical_cases', 'clinical_cases')
        .where('progress.user_id = :userId', { userId })
        .andWhere('clinical_cases.id = :caseId', { caseId })
        .getMany();

      for (const pathProgress of pathProgresses) {
        await this.updatePathProgressFromClinicalCase(pathProgress, caseId, attemptData);
      }

      // Update related goals
      await this.updateGoalsFromClinicalCaseCompletion(userId, caseId, attemptData);
    } catch (error) {
      this.logger.error(`Error syncing clinical case completion: ${error.message}`, error.stack);
    }
  }

  /**
   * Auto-enroll users in learning paths based on course enrollments
   */
  async autoEnrollInRecommendedPaths(userId: string, courseId: string): Promise<void> {
    try {
      // Find learning paths that include this course and are marked for auto-enrollment
      const recommendedPaths = await this.pathProgressRepository
        .createQueryBuilder('path')
        .leftJoinAndSelect('path.courses', 'courses')
        .where('courses.id = :courseId', { courseId })
        .andWhere('path.type = :type', { type: 'template' })
        .andWhere('path.status = :status', { status: 'published' })
        .getMany();

      for (const path of recommendedPaths) {
        // Check if user is already enrolled
        const existingProgress = await this.pathProgressRepository.findOne({
          where: { user_id: userId, learning_path_id: path.id },
        });

        if (!existingProgress) {
          // Auto-enroll with default preferences
          await this.pathProgressService.startLearningPath(userId, {
            learning_path_id: path.id,
            preferences: {
              daily_study_goal_minutes: 60,
              reminder_settings: {
                enabled: true,
                frequency: 'daily',
                time: '09:00',
              },
            },
          });

          this.logger.log(`Auto-enrolled user ${userId} in learning path ${path.id}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error auto-enrolling in recommended paths: ${error.message}`, error.stack);
    }
  }

  /**
   * Create learning goals from learning path milestones
   */
  async createGoalsFromPathMilestones(userId: string, pathId: string): Promise<void> {
    try {
      const pathProgress = await this.pathProgressRepository.findOne({
        where: { user_id: userId, learning_path_id: pathId },
        relations: ['learning_path', 'learning_path.milestones'],
      });

      if (!pathProgress || !pathProgress.learning_path.milestones) {
        return;
      }

      for (const milestone of pathProgress.learning_path.milestones) {
        // Check if goal already exists for this milestone
        const existingGoal = await this.goalRepository.findOne({
          where: {
            user_id: userId,
            learning_path_id: pathId,
            title: `Milestone: ${milestone.title}`,
          },
        });

        if (!existingGoal) {
          // Create goal from milestone
          await this.goalsService.create({
            title: `Milestone: ${milestone.title}`,
            description: milestone.description,
            type: 'custom',
            category: 'learning_path',
            priority: milestone.is_required ? 'high' : 'medium',
            target_criteria: {
              type: 'boolean',
              target_value: true,
              measurement_method: 'milestone_completion',
            },
            start_date: new Date(),
            target_date: this.calculateMilestoneTargetDate(pathProgress, milestone),
            learning_path_id: pathId,
            smart_criteria: {
              specific: `Complete milestone: ${milestone.title}`,
              measurable: 'Milestone completion status',
              achievable: 'Based on learning path structure',
              relevant: 'Part of structured learning journey',
              time_bound: 'Aligned with learning path timeline',
            },
          }, userId);

          this.logger.log(`Created goal for milestone ${milestone.id} for user ${userId}`);
        }
      }
    } catch (error) {
      this.logger.error(`Error creating goals from path milestones: ${error.message}`, error.stack);
    }
  }

  /**
   * Update learning path progress from course completion
   */
  private async updatePathProgressFromCourse(
    pathProgress: LearningPathProgress,
    courseId: string,
    progressData: any
  ): Promise<void> {
    // Find the module in the path structure that corresponds to this course
    const pathStructure = pathProgress.learning_path.path_structure;
    
    for (const phase of pathStructure.phases) {
      for (const module of phase.modules) {
        if (module.type === 'course' && module.resource_id === courseId) {
          // Update module progress
          await this.pathProgressService.updateProgress(
            pathProgress.user_id,
            pathProgress.learning_path_id,
            {
              phase_id: phase.id,
              module_id: module.id,
              progress_percentage: progressData.progress_percentage || 0,
              time_spent_minutes: progressData.time_spent_minutes || 0,
              module_status: progressData.status === 'completed' ? 'completed' : 'in_progress',
            }
          );
          break;
        }
      }
    }
  }

  /**
   * Update learning path progress from assessment completion
   */
  private async updatePathProgressFromAssessment(
    pathProgress: LearningPathProgress,
    assessmentId: string,
    attemptData: any
  ): Promise<void> {
    const pathStructure = pathProgress.learning_path.path_structure;
    
    for (const phase of pathStructure.phases) {
      for (const module of phase.modules) {
        if (module.type === 'assessment' && module.resource_id === assessmentId) {
          const progressPercentage = attemptData.passed ? 100 : (attemptData.score || 0);
          
          await this.pathProgressService.updateProgress(
            pathProgress.user_id,
            pathProgress.learning_path_id,
            {
              phase_id: phase.id,
              module_id: module.id,
              progress_percentage: progressPercentage,
              time_spent_minutes: attemptData.time_taken_minutes || 0,
              module_status: attemptData.passed ? 'completed' : 'in_progress',
              score: attemptData.score,
            }
          );
          break;
        }
      }
    }
  }

  /**
   * Update learning path progress from clinical case completion
   */
  private async updatePathProgressFromClinicalCase(
    pathProgress: LearningPathProgress,
    caseId: string,
    attemptData: any
  ): Promise<void> {
    const pathStructure = pathProgress.learning_path.path_structure;
    
    for (const phase of pathStructure.phases) {
      for (const module of phase.modules) {
        if (module.type === 'clinical_case' && module.resource_id === caseId) {
          const progressPercentage = attemptData.completed ? 100 : (attemptData.progress_percentage || 0);
          
          await this.pathProgressService.updateProgress(
            pathProgress.user_id,
            pathProgress.learning_path_id,
            {
              phase_id: phase.id,
              module_id: module.id,
              progress_percentage: progressPercentage,
              time_spent_minutes: attemptData.time_spent_minutes || 0,
              module_status: attemptData.completed ? 'completed' : 'in_progress',
              score: attemptData.final_score,
            }
          );
          break;
        }
      }
    }
  }

  /**
   * Update goals based on course progress
   */
  private async updateGoalsFromCourseProgress(
    userId: string,
    courseId: string,
    progressData: any
  ): Promise<void> {
    // Find goals related to this course
    const relatedGoals = await this.goalRepository.find({
      where: {
        user_id: userId,
        course_id: courseId,
        status: 'active',
      },
    });

    for (const goal of relatedGoals) {
      if (goal.category === 'course_completion' && progressData.status === 'completed') {
        // Mark goal as completed
        await this.goalsService.update(goal.id, {
          status: 'completed',
          progress_percentage: 100,
        }, userId);
      } else if (goal.category === 'study_time' && progressData.time_spent_minutes) {
        // Update study time goal
        const currentValue = Number(goal.target_criteria.current_value) || 0;
        const newValue = currentValue + progressData.time_spent_minutes;
        
        await this.goalsService.addProgressEntry(goal.id, userId, {
          entry_type: 'automatic',
          progress_value: newValue,
          notes: `Course progress update: ${courseId}`,
          metadata: { source: 'course_progress', course_id: courseId },
        });
      }
    }
  }

  /**
   * Update goals based on assessment results
   */
  private async updateGoalsFromAssessmentResults(
    userId: string,
    assessmentId: string,
    attemptData: any
  ): Promise<void> {
    const relatedGoals = await this.goalRepository.find({
      where: {
        user_id: userId,
        status: 'active',
      },
    });

    for (const goal of relatedGoals) {
      if (goal.category === 'assessment_score' && attemptData.score) {
        const targetScore = Number(goal.target_criteria.target_value);
        if (attemptData.score >= targetScore) {
          await this.goalsService.addProgressEntry(goal.id, userId, {
            entry_type: 'automatic',
            progress_value: attemptData.score,
            notes: `Assessment completed: ${assessmentId}`,
            metadata: { source: 'assessment_result', assessment_id: assessmentId },
          });
        }
      }
    }
  }

  /**
   * Update goals based on clinical case completion
   */
  private async updateGoalsFromClinicalCaseCompletion(
    userId: string,
    caseId: string,
    attemptData: any
  ): Promise<void> {
    const relatedGoals = await this.goalRepository.find({
      where: {
        user_id: userId,
        category: 'clinical_cases',
        status: 'active',
      },
    });

    for (const goal of relatedGoals) {
      if (attemptData.completed) {
        const currentValue = Number(goal.target_criteria.current_value) || 0;
        const newValue = currentValue + 1;
        
        await this.goalsService.addProgressEntry(goal.id, userId, {
          entry_type: 'automatic',
          progress_value: newValue,
          notes: `Clinical case completed: ${caseId}`,
          metadata: { source: 'clinical_case_completion', case_id: caseId },
        });
      }
    }
  }

  /**
   * Calculate target date for milestone based on learning path timeline
   */
  private calculateMilestoneTargetDate(
    pathProgress: LearningPathProgress,
    milestone: any
  ): Date {
    const startDate = pathProgress.started_at || new Date();
    const pathDurationWeeks = pathProgress.learning_path.estimated_duration_weeks;
    const milestoneOrder = milestone.order;
    const totalMilestones = pathProgress.learning_path.milestones.length;
    
    // Distribute milestones evenly across the path duration
    const milestoneWeeks = (pathDurationWeeks / totalMilestones) * (milestoneOrder + 1);
    const targetDate = new Date(startDate);
    targetDate.setDate(targetDate.getDate() + (milestoneWeeks * 7));
    
    return targetDate;
  }
}
