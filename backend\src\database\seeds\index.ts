import { DataSource } from 'typeorm';
import { seedLearningPathsSimple } from './learning-paths-simple.seed';
import { seedLearningGoalsSimple } from './learning-goals-simple.seed';

export async function runSeeds(dataSource: DataSource) {
  console.log('Starting database seeding...');

  try {
    // Seed learning paths first (goals may reference paths)
    console.log('Seeding learning paths...');
    await seedLearningPathsSimple(dataSource);

    // Seed learning goals
    console.log('Seeding learning goals...');
    await seedLearningGoalsSimple(dataSource);

    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error during database seeding:', error);
    throw error;
  }
}
