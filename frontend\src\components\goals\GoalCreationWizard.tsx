'use client';

import React, { useState, useEffect } from 'react';
import { 
  Target, 
  Calendar, 
  Clock, 
  BookOpen, 
  TrendingUp, 
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  X,
  Lightbulb,
  Star,
  AlertCircle
} from 'lucide-react';

interface GoalCreationWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onGoalCreated: () => void;
}

interface SMARTSuggestions {
  specific: string;
  measurable: string;
  achievable: string;
  relevant: string;
  time_bound: string;
}

export const GoalCreationWizard: React.FC<GoalCreationWizardProps> = ({
  isOpen,
  onClose,
  onGoalCreated,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [goalData, setGoalData] = useState({
    title: '',
    description: '',
    type: 'custom',
    category: 'custom',
    priority: 'medium',
    target_criteria: {
      type: 'numeric',
      target_value: 0,
      unit: '',
      measurement_method: '',
    },
    start_date: new Date().toISOString().split('T')[0],
    target_date: '',
    learning_path_id: '',
    course_id: '',
    smart_criteria: {
      specific: '',
      measurable: '',
      achievable: '',
      relevant: '',
      time_bound: '',
    },
    milestones: [] as any[],
    reminder_settings: {
      enabled: true,
      frequency: 'daily' as const,
      time: '09:00',
    },
    progress_tracking: {
      auto_tracking: false,
      manual_updates: true,
      data_sources: [] as string[],
      update_frequency: 'manual' as const,
    },
  });
  const [smartSuggestions, setSmartSuggestions] = useState<SMARTSuggestions | null>(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const steps = [
    { id: 1, title: 'Goal Basics', description: 'Define your goal' },
    { id: 2, title: 'SMART Criteria', description: 'Make it specific and measurable' },
    { id: 3, title: 'Timeline & Milestones', description: 'Set deadlines and checkpoints' },
    { id: 4, title: 'Tracking & Reminders', description: 'Configure progress tracking' },
    { id: 5, title: 'Review & Create', description: 'Confirm your goal' },
  ];

  useEffect(() => {
    if (currentStep === 2 && goalData.title && goalData.category) {
      generateSMARTSuggestions();
    }
  }, [currentStep, goalData.title, goalData.category]);

  const generateSMARTSuggestions = async () => {
    try {
      const response = await fetch('/api/learning-goals/smart-suggestions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: goalData.title,
          category: goalData.category,
          target_criteria: goalData.target_criteria,
        }),
      });
      
      if (response.ok) {
        const suggestions = await response.json();
        setSmartSuggestions(suggestions);
        setGoalData(prev => ({
          ...prev,
          smart_criteria: suggestions,
        }));
      }
    } catch (error) {
      console.error('Error generating SMART suggestions:', error);
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: { [key: string]: string } = {};

    switch (step) {
      case 1:
        if (!goalData.title.trim()) newErrors.title = 'Title is required';
        if (!goalData.description.trim()) newErrors.description = 'Description is required';
        if (!goalData.target_criteria.target_value) newErrors.target_value = 'Target value is required';
        break;
      
      case 2:
        if (!goalData.smart_criteria.specific.trim()) newErrors.specific = 'Specific criteria is required';
        if (!goalData.smart_criteria.measurable.trim()) newErrors.measurable = 'Measurable criteria is required';
        break;
      
      case 3:
        if (!goalData.target_date) newErrors.target_date = 'Target date is required';
        if (new Date(goalData.target_date) <= new Date(goalData.start_date)) {
          newErrors.target_date = 'Target date must be after start date';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const createGoal = async () => {
    if (!validateStep(currentStep)) return;

    try {
      setLoading(true);
      const response = await fetch('/api/learning-goals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...goalData,
          start_date: new Date(goalData.start_date),
          target_date: new Date(goalData.target_date),
        }),
      });

      if (response.ok) {
        onGoalCreated();
        onClose();
        resetForm();
      } else {
        const error = await response.json();
        setErrors({ general: error.message || 'Failed to create goal' });
      }
    } catch (error) {
      setErrors({ general: 'Failed to create goal' });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setCurrentStep(1);
    setGoalData({
      title: '',
      description: '',
      type: 'custom',
      category: 'custom',
      priority: 'medium',
      target_criteria: {
        type: 'numeric',
        target_value: 0,
        unit: '',
        measurement_method: '',
      },
      start_date: new Date().toISOString().split('T')[0],
      target_date: '',
      learning_path_id: '',
      course_id: '',
      smart_criteria: {
        specific: '',
        measurable: '',
        achievable: '',
        relevant: '',
        time_bound: '',
      },
      milestones: [],
      reminder_settings: {
        enabled: true,
        frequency: 'daily',
        time: '09:00',
      },
      progress_tracking: {
        auto_tracking: false,
        manual_updates: true,
        data_sources: [],
        update_frequency: 'manual',
      },
    });
    setErrors({});
    setSmartSuggestions(null);
  };

  const addMilestone = () => {
    const newMilestone = {
      title: '',
      description: '',
      target_date: goalData.target_date,
      target_value: goalData.target_criteria.target_value / 2,
      order: goalData.milestones.length,
    };
    setGoalData(prev => ({
      ...prev,
      milestones: [...prev.milestones, newMilestone],
    }));
  };

  const updateMilestone = (index: number, field: string, value: any) => {
    setGoalData(prev => ({
      ...prev,
      milestones: prev.milestones.map((milestone, i) =>
        i === index ? { ...milestone, [field]: value } : milestone
      ),
    }));
  };

  const removeMilestone = (index: number) => {
    setGoalData(prev => ({
      ...prev,
      milestones: prev.milestones.filter((_, i) => i !== index),
    }));
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Goal Title *
        </label>
        <input
          type="text"
          value={goalData.title}
          onChange={(e) => setGoalData(prev => ({ ...prev, title: e.target.value }))}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.title ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="e.g., Complete USMLE Step 1 preparation"
        />
        {errors.title && <p className="text-red-600 text-sm mt-1">{errors.title}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description *
        </label>
        <textarea
          value={goalData.description}
          onChange={(e) => setGoalData(prev => ({ ...prev, description: e.target.value }))}
          rows={3}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
            errors.description ? 'border-red-300' : 'border-gray-300'
          }`}
          placeholder="Describe what you want to achieve and why it's important"
        />
        {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Category
          </label>
          <select
            value={goalData.category}
            onChange={(e) => setGoalData(prev => ({ ...prev, category: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="study_time">Study Time</option>
            <option value="course_completion">Course Completion</option>
            <option value="assessment_score">Assessment Score</option>
            <option value="skill_mastery">Skill Mastery</option>
            <option value="streak_maintenance">Streak Maintenance</option>
            <option value="custom">Custom</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Priority
          </label>
          <select
            value={goalData.priority}
            onChange={(e) => setGoalData(prev => ({ ...prev, priority: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Value *
          </label>
          <input
            type="number"
            value={goalData.target_criteria.target_value}
            onChange={(e) => setGoalData(prev => ({
              ...prev,
              target_criteria: { ...prev.target_criteria, target_value: parseFloat(e.target.value) || 0 }
            }))}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.target_value ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="100"
          />
          {errors.target_value && <p className="text-red-600 text-sm mt-1">{errors.target_value}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Unit
          </label>
          <input
            type="text"
            value={goalData.target_criteria.unit}
            onChange={(e) => setGoalData(prev => ({
              ...prev,
              target_criteria: { ...prev.target_criteria, unit: e.target.value }
            }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="hours, points, %"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Measurement Type
          </label>
          <select
            value={goalData.target_criteria.type}
            onChange={(e) => setGoalData(prev => ({
              ...prev,
              target_criteria: { ...prev.target_criteria, type: e.target.value }
            }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="numeric">Numeric</option>
            <option value="percentage">Percentage</option>
            <option value="boolean">Yes/No</option>
            <option value="completion">Completion</option>
            <option value="score">Score</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div className="flex items-start space-x-3">
          <Lightbulb className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900 mb-1">SMART Goal Framework</h4>
            <p className="text-blue-800 text-sm">
              Make your goal Specific, Measurable, Achievable, Relevant, and Time-bound for better success.
            </p>
          </div>
        </div>
      </div>

      {smartSuggestions && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-3">
            <Star className="w-5 h-5 text-green-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-green-900 mb-1">AI Suggestions Generated</h4>
              <p className="text-green-800 text-sm">
                We've generated SMART criteria suggestions based on your goal. Feel free to customize them.
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Specific - What exactly will you accomplish? *
          </label>
          <textarea
            value={goalData.smart_criteria.specific}
            onChange={(e) => setGoalData(prev => ({
              ...prev,
              smart_criteria: { ...prev.smart_criteria, specific: e.target.value }
            }))}
            rows={2}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.specific ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Be specific about what you want to achieve"
          />
          {errors.specific && <p className="text-red-600 text-sm mt-1">{errors.specific}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Measurable - How will you measure progress? *
          </label>
          <textarea
            value={goalData.smart_criteria.measurable}
            onChange={(e) => setGoalData(prev => ({
              ...prev,
              smart_criteria: { ...prev.smart_criteria, measurable: e.target.value }
            }))}
            rows={2}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.measurable ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Define how you'll track and measure success"
          />
          {errors.measurable && <p className="text-red-600 text-sm mt-1">{errors.measurable}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Achievable - Is this goal realistic?
          </label>
          <textarea
            value={goalData.smart_criteria.achievable}
            onChange={(e) => setGoalData(prev => ({
              ...prev,
              smart_criteria: { ...prev.smart_criteria, achievable: e.target.value }
            }))}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Explain why this goal is achievable for you"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Relevant - Why is this goal important?
          </label>
          <textarea
            value={goalData.smart_criteria.relevant}
            onChange={(e) => setGoalData(prev => ({
              ...prev,
              smart_criteria: { ...prev.smart_criteria, relevant: e.target.value }
            }))}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Connect this goal to your broader objectives"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Time-bound - When will you complete this?
          </label>
          <textarea
            value={goalData.smart_criteria.time_bound}
            onChange={(e) => setGoalData(prev => ({
              ...prev,
              smart_criteria: { ...prev.smart_criteria, time_bound: e.target.value }
            }))}
            rows={2}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Set a specific deadline and timeline"
          />
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Start Date
          </label>
          <input
            type="date"
            value={goalData.start_date}
            onChange={(e) => setGoalData(prev => ({ ...prev, start_date: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Date *
          </label>
          <input
            type="date"
            value={goalData.target_date}
            onChange={(e) => setGoalData(prev => ({ ...prev, target_date: e.target.value }))}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
              errors.target_date ? 'border-red-300' : 'border-gray-300'
            }`}
          />
          {errors.target_date && <p className="text-red-600 text-sm mt-1">{errors.target_date}</p>}
        </div>
      </div>

      <div>
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-medium text-gray-900">Milestones</h4>
          <button
            type="button"
            onClick={addMilestone}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            + Add Milestone
          </button>
        </div>

        {goalData.milestones.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <Target className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500 text-sm">
              Add milestones to break your goal into smaller, manageable steps
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {goalData.milestones.map((milestone, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <h5 className="font-medium text-gray-900">Milestone {index + 1}</h5>
                  <button
                    type="button"
                    onClick={() => removeMilestone(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Title
                    </label>
                    <input
                      type="text"
                      value={milestone.title}
                      onChange={(e) => updateMilestone(index, 'title', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Milestone title"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Target Date
                    </label>
                    <input
                      type="date"
                      value={milestone.target_date}
                      onChange={(e) => updateMilestone(index, 'target_date', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Target Value
                  </label>
                  <input
                    type="number"
                    value={milestone.target_value}
                    onChange={(e) => updateMilestone(index, 'target_value', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Target value for this milestone"
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Progress Tracking</h4>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="manual_updates"
              checked={goalData.progress_tracking.manual_updates}
              onChange={(e) => setGoalData(prev => ({
                ...prev,
                progress_tracking: { ...prev.progress_tracking, manual_updates: e.target.checked }
              }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="manual_updates" className="text-sm font-medium text-gray-700">
              Allow manual progress updates
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="auto_tracking"
              checked={goalData.progress_tracking.auto_tracking}
              onChange={(e) => setGoalData(prev => ({
                ...prev,
                progress_tracking: { ...prev.progress_tracking, auto_tracking: e.target.checked }
              }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="auto_tracking" className="text-sm font-medium text-gray-700">
              Enable automatic progress tracking (when possible)
            </label>
          </div>
        </div>
      </div>

      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">Reminders</h4>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="reminders_enabled"
              checked={goalData.reminder_settings.enabled}
              onChange={(e) => setGoalData(prev => ({
                ...prev,
                reminder_settings: { ...prev.reminder_settings, enabled: e.target.checked }
              }))}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="reminders_enabled" className="text-sm font-medium text-gray-700">
              Enable goal reminders
            </label>
          </div>

          {goalData.reminder_settings.enabled && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Frequency
                </label>
                <select
                  value={goalData.reminder_settings.frequency}
                  onChange={(e) => setGoalData(prev => ({
                    ...prev,
                    reminder_settings: { ...prev.reminder_settings, frequency: e.target.value as any }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="custom">Custom</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time
                </label>
                <input
                  type="time"
                  value={goalData.reminder_settings.time}
                  onChange={(e) => setGoalData(prev => ({
                    ...prev,
                    reminder_settings: { ...prev.reminder_settings, time: e.target.value }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderStep5 = () => (
    <div className="space-y-6">
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Goal Summary</h4>
        
        <div className="space-y-4">
          <div>
            <h5 className="font-medium text-gray-700">Title</h5>
            <p className="text-gray-900">{goalData.title}</p>
          </div>

          <div>
            <h5 className="font-medium text-gray-700">Description</h5>
            <p className="text-gray-900">{goalData.description}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h5 className="font-medium text-gray-700">Target</h5>
              <p className="text-gray-900">
                {goalData.target_criteria.target_value} {goalData.target_criteria.unit}
              </p>
            </div>

            <div>
              <h5 className="font-medium text-gray-700">Deadline</h5>
              <p className="text-gray-900">
                {new Date(goalData.target_date).toLocaleDateString()}
              </p>
            </div>
          </div>

          <div>
            <h5 className="font-medium text-gray-700">Priority</h5>
            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
              goalData.priority === 'critical' ? 'bg-red-100 text-red-800' :
              goalData.priority === 'high' ? 'bg-orange-100 text-orange-800' :
              goalData.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {goalData.priority}
            </span>
          </div>

          {goalData.milestones.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-700">Milestones</h5>
              <ul className="list-disc list-inside text-gray-900 space-y-1">
                {goalData.milestones.map((milestone, index) => (
                  <li key={index}>{milestone.title}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {errors.general && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
            <p className="text-red-800">{errors.general}</p>
          </div>
        </div>
      )}
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={onClose} />

        <div className="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Create Learning Goal</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Progress indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    currentStep >= step.id
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {currentStep > step.id ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <span className="text-sm font-medium">{step.id}</span>
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-2 ${
                      currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            <div className="mt-2">
              <h3 className="text-lg font-medium text-gray-900">
                {steps[currentStep - 1].title}
              </h3>
              <p className="text-sm text-gray-600">
                {steps[currentStep - 1].description}
              </p>
            </div>
          </div>

          {/* Step content */}
          <div className="mb-8">
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}
            {currentStep === 5 && renderStep5()}
          </div>

          {/* Navigation buttons */}
          <div className="flex items-center justify-between">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                currentStep === 1
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Previous</span>
            </button>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </button>
              
              {currentStep < steps.length ? (
                <button
                  onClick={nextStep}
                  className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <span>Next</span>
                  <ArrowRight className="w-4 h-4" />
                </button>
              ) : (
                <button
                  onClick={createGoal}
                  disabled={loading}
                  className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                  ) : (
                    <CheckCircle className="w-4 h-4" />
                  )}
                  <span>Create Goal</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
