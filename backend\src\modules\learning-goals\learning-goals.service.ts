import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { LearningGoal, GoalType, GoalCategory, GoalStatus, GoalPriority } from '../../entities/learning-goal.entity';
import { LearningGoalProgress, ProgressEntryType } from '../../entities/learning-goal-progress.entity';
import { User } from '../../entities/user.entity';
import { LearningPath } from '../../entities/learning-path.entity';
import { Course } from '../../entities/course.entity';

export interface CreateLearningGoalDto {
  title: string;
  description: string;
  type: GoalType;
  category: GoalCategory;
  priority?: GoalPriority;
  target_criteria: {
    type: 'numeric' | 'percentage' | 'boolean' | 'completion' | 'score';
    target_value: number | boolean | string;
    unit?: string;
    measurement_method?: string;
  };
  start_date: Date;
  target_date: Date;
  learning_path_id?: string;
  course_id?: string;
  related_resources?: string[];
  smart_criteria?: {
    specific: string;
    measurable: string;
    achievable: string;
    relevant: string;
    time_bound: string;
  };
  milestones?: {
    title: string;
    description: string;
    target_date: Date;
    target_value: number;
    order: number;
  }[];
  reminder_settings?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'custom';
    time: string;
    days_of_week?: number[];
    custom_schedule?: { date: Date; time: string }[];
  };
  progress_tracking?: {
    auto_tracking: boolean;
    manual_updates: boolean;
    data_sources: string[];
    update_frequency: 'real_time' | 'daily' | 'weekly' | 'manual';
  };
}

export interface UpdateLearningGoalDto extends Partial<CreateLearningGoalDto> {
  status?: GoalStatus;
  progress_percentage?: number;
  notes?: string;
}

export interface GoalFilters {
  status?: GoalStatus;
  category?: GoalCategory;
  type?: GoalType;
  priority?: GoalPriority;
  due_soon?: boolean;
  overdue?: boolean;
  search?: string;
}

export interface GoalAnalytics {
  total_goals: number;
  active_goals: number;
  completed_goals: number;
  overdue_goals: number;
  completion_rate: number;
  average_completion_time_days: number;
  goals_by_category: { [key: string]: number };
  goals_by_priority: { [key: string]: number };
  streak_data: {
    current_streak: number;
    longest_streak: number;
    streak_goals: string[];
  };
  upcoming_deadlines: {
    goal_id: string;
    title: string;
    target_date: Date;
    days_remaining: number;
  }[];
}

@Injectable()
export class LearningGoalsService {
  constructor(
    @InjectRepository(LearningGoal)
    private goalRepository: Repository<LearningGoal>,
    @InjectRepository(LearningGoalProgress)
    private progressRepository: Repository<LearningGoalProgress>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(LearningPath)
    private learningPathRepository: Repository<LearningPath>,
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
  ) {}

  async create(createDto: CreateLearningGoalDto, userId: string): Promise<LearningGoal> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Validate related resources
    let learningPath = null;
    let course = null;

    if (createDto.learning_path_id) {
      learningPath = await this.learningPathRepository.findOne({
        where: { id: createDto.learning_path_id },
      });
      if (!learningPath) {
        throw new NotFoundException('Learning path not found');
      }
    }

    if (createDto.course_id) {
      course = await this.courseRepository.findOne({
        where: { id: createDto.course_id },
      });
      if (!course) {
        throw new NotFoundException('Course not found');
      }
    }

    // Generate milestones with IDs
    const milestones = createDto.milestones?.map((milestone, index) => ({
      ...milestone,
      id: `milestone_${Date.now()}_${index}`,
      completed: false,
    })) || [];

    const goal = this.goalRepository.create({
      ...createDto,
      user,
      learning_path: learningPath,
      course,
      milestones,
      progress_percentage: 0,
      streak_count: 0,
      analytics: {
        time_spent_minutes: 0,
        sessions_completed: 0,
        average_session_duration: 0,
        consistency_score: 0,
        velocity_trend: 'stable',
      },
    });

    const savedGoal = await this.goalRepository.save(goal);

    // Create initial progress entry
    await this.addProgressEntry(savedGoal.id, userId, {
      entry_type: ProgressEntryType.AUTOMATIC,
      progress_value: 0,
      notes: 'Goal created',
      metadata: { auto_generated: true },
    });

    return savedGoal;
  }

  async findAll(userId: string, filters: GoalFilters = {}): Promise<LearningGoal[]> {
    const queryBuilder = this.goalRepository.createQueryBuilder('goal')
      .leftJoinAndSelect('goal.learning_path', 'learning_path')
      .leftJoinAndSelect('goal.course', 'course')
      .leftJoinAndSelect('goal.progress_entries', 'progress_entries')
      .where('goal.user_id = :userId', { userId });

    if (filters.status) {
      queryBuilder.andWhere('goal.status = :status', { status: filters.status });
    }

    if (filters.category) {
      queryBuilder.andWhere('goal.category = :category', { category: filters.category });
    }

    if (filters.type) {
      queryBuilder.andWhere('goal.type = :type', { type: filters.type });
    }

    if (filters.priority) {
      queryBuilder.andWhere('goal.priority = :priority', { priority: filters.priority });
    }

    if (filters.due_soon) {
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);
      queryBuilder.andWhere('goal.target_date <= :nextWeek', { nextWeek });
    }

    if (filters.overdue) {
      const now = new Date();
      queryBuilder.andWhere('goal.target_date < :now AND goal.status != :completed', {
        now,
        completed: GoalStatus.COMPLETED,
      });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(goal.title ILIKE :search OR goal.description ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    queryBuilder.orderBy('goal.priority', 'DESC')
      .addOrderBy('goal.target_date', 'ASC');

    return await queryBuilder.getMany();
  }

  async findOne(id: string, userId: string): Promise<LearningGoal> {
    const goal = await this.goalRepository.findOne({
      where: { id, user_id: userId },
      relations: ['learning_path', 'course', 'progress_entries'],
    });

    if (!goal) {
      throw new NotFoundException('Learning goal not found');
    }

    return goal;
  }

  async update(id: string, updateDto: UpdateLearningGoalDto, userId: string): Promise<LearningGoal> {
    const goal = await this.findOne(id, userId);

    // Handle status changes
    if (updateDto.status && updateDto.status !== goal.status) {
      if (updateDto.status === GoalStatus.COMPLETED) {
        goal.completed_at = new Date();
        goal.progress_percentage = 100;
        
        // Add completion progress entry
        await this.addProgressEntry(id, userId, {
          entry_type: ProgressEntryType.AUTOMATIC,
          progress_value: Number(goal.target_criteria.target_value),
          notes: 'Goal completed',
          metadata: { auto_generated: true },
          celebration_data: {
            triggered: true,
            type: 'completion',
            message: `Congratulations! You've completed your goal: ${goal.title}`,
            points_awarded: this.calculateCompletionPoints(goal),
          },
        });
      }
    }

    // Update milestone completion if provided
    if (updateDto.milestones) {
      goal.milestones = updateDto.milestones.map(milestone => ({
        ...milestone,
        id: milestone.id || `milestone_${Date.now()}_${Math.random()}`,
      }));
    }

    Object.assign(goal, updateDto);
    return await this.goalRepository.save(goal);
  }

  async remove(id: string, userId: string): Promise<void> {
    const goal = await this.findOne(id, userId);
    await this.goalRepository.remove(goal);
  }

  async addProgressEntry(
    goalId: string,
    userId: string,
    progressData: {
      entry_type: ProgressEntryType;
      progress_value: number;
      notes?: string;
      metadata?: any;
      context?: any;
      celebration_data?: any;
    }
  ): Promise<LearningGoalProgress> {
    const goal = await this.findOne(goalId, userId);
    
    // Calculate progress percentage
    const targetValue = Number(goal.target_criteria.target_value);
    const progressPercentage = targetValue > 0 ? Math.min((progressData.progress_value / targetValue) * 100, 100) : 0;
    
    // Calculate delta from previous value
    const previousValue = Number(goal.target_criteria.current_value) || 0;
    const deltaValue = progressData.progress_value - previousValue;

    const progressEntry = this.progressRepository.create({
      learning_goal_id: goalId,
      user_id: userId,
      entry_type: progressData.entry_type,
      progress_value: progressData.progress_value,
      progress_percentage: progressPercentage,
      previous_value: previousValue,
      delta_value: deltaValue,
      unit: goal.target_criteria.unit,
      notes: progressData.notes,
      metadata: progressData.metadata,
      context: progressData.context,
      recorded_at: new Date(),
      celebration_data: progressData.celebration_data,
    });

    const savedProgress = await this.progressRepository.save(progressEntry);

    // Update goal's current value and progress
    goal.target_criteria.current_value = progressData.progress_value;
    goal.progress_percentage = progressPercentage;
    goal.last_progress_update = new Date();

    // Check for milestone achievements
    await this.checkMilestoneAchievements(goal, progressData.progress_value);

    // Update streak if applicable
    await this.updateStreak(goal);

    await this.goalRepository.save(goal);

    return savedProgress;
  }

  async getAnalytics(userId: string): Promise<GoalAnalytics> {
    const goals = await this.goalRepository.find({
      where: { user_id: userId },
      relations: ['progress_entries'],
    });

    const totalGoals = goals.length;
    const activeGoals = goals.filter(g => g.status === GoalStatus.ACTIVE).length;
    const completedGoals = goals.filter(g => g.status === GoalStatus.COMPLETED).length;
    const overdueGoals = goals.filter(g => 
      g.status === GoalStatus.ACTIVE && g.target_date < new Date()
    ).length;

    const completionRate = totalGoals > 0 ? (completedGoals / totalGoals) * 100 : 0;

    // Calculate average completion time
    const completedGoalsWithDates = goals.filter(g => 
      g.status === GoalStatus.COMPLETED && g.completed_at && g.start_date
    );
    const avgCompletionTime = completedGoalsWithDates.length > 0 
      ? completedGoalsWithDates.reduce((sum, goal) => {
          const days = (goal.completed_at!.getTime() - goal.start_date.getTime()) / (1000 * 60 * 60 * 24);
          return sum + days;
        }, 0) / completedGoalsWithDates.length
      : 0;

    // Goals by category
    const goalsByCategory = goals.reduce((acc, goal) => {
      acc[goal.category] = (acc[goal.category] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    // Goals by priority
    const goalsByPriority = goals.reduce((acc, goal) => {
      acc[goal.priority] = (acc[goal.priority] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });

    // Streak data
    const streakGoals = goals.filter(g => g.streak_count > 0);
    const currentStreak = streakGoals.reduce((max, goal) => Math.max(max, goal.streak_count), 0);
    const longestStreak = goals.reduce((max, goal) => {
      const maxStreakInGoal = goal.analytics?.consistency_score || 0;
      return Math.max(max, maxStreakInGoal);
    }, 0);

    // Upcoming deadlines
    const upcomingDeadlines = goals
      .filter(g => g.status === GoalStatus.ACTIVE && g.target_date > new Date())
      .map(goal => ({
        goal_id: goal.id,
        title: goal.title,
        target_date: goal.target_date,
        days_remaining: Math.ceil((goal.target_date.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)),
      }))
      .sort((a, b) => a.days_remaining - b.days_remaining)
      .slice(0, 5);

    return {
      total_goals: totalGoals,
      active_goals: activeGoals,
      completed_goals: completedGoals,
      overdue_goals: overdueGoals,
      completion_rate: completionRate,
      average_completion_time_days: avgCompletionTime,
      goals_by_category: goalsByCategory,
      goals_by_priority: goalsByPriority,
      streak_data: {
        current_streak: currentStreak,
        longest_streak: longestStreak,
        streak_goals: streakGoals.map(g => g.id),
      },
      upcoming_deadlines: upcomingDeadlines,
    };
  }

  async getGoalProgress(goalId: string, userId: string): Promise<LearningGoalProgress[]> {
    const goal = await this.findOne(goalId, userId);
    
    return await this.progressRepository.find({
      where: { learning_goal_id: goalId },
      order: { recorded_at: 'DESC' },
    });
  }

  async generateSMARTSuggestions(goalData: Partial<CreateLearningGoalDto>): Promise<any> {
    // AI-powered SMART criteria suggestions based on goal data
    const suggestions = {
      specific: this.generateSpecificSuggestion(goalData),
      measurable: this.generateMeasurableSuggestion(goalData),
      achievable: this.generateAchievableSuggestion(goalData),
      relevant: this.generateRelevantSuggestion(goalData),
      time_bound: this.generateTimeBoundSuggestion(goalData),
    };

    return suggestions;
  }

  private async checkMilestoneAchievements(goal: LearningGoal, currentValue: number): Promise<void> {
    if (!goal.milestones) return;

    for (const milestone of goal.milestones) {
      if (!milestone.completed && currentValue >= milestone.target_value) {
        milestone.completed = true;
        milestone.completed_at = new Date();

        // Add milestone progress entry
        await this.addProgressEntry(goal.id, goal.user_id, {
          entry_type: ProgressEntryType.MILESTONE,
          progress_value: currentValue,
          notes: `Milestone achieved: ${milestone.title}`,
          metadata: { milestone_id: milestone.id },
          celebration_data: {
            triggered: true,
            type: 'milestone',
            message: `Milestone achieved: ${milestone.title}`,
            points_awarded: 50,
          },
        });
      }
    }
  }

  private async updateStreak(goal: LearningGoal): Promise<void> {
    const today = new Date();
    const lastUpdate = goal.last_progress_update;
    
    if (!lastUpdate) {
      goal.streak_count = 1;
      return;
    }

    const daysDiff = Math.floor((today.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff === 1) {
      goal.streak_count += 1;
    } else if (daysDiff > 1) {
      goal.streak_count = 1;
    }
  }

  private calculateCompletionPoints(goal: LearningGoal): number {
    let points = 100; // Base points
    
    // Bonus for priority
    switch (goal.priority) {
      case GoalPriority.CRITICAL:
        points += 50;
        break;
      case GoalPriority.HIGH:
        points += 30;
        break;
      case GoalPriority.MEDIUM:
        points += 10;
        break;
    }

    // Bonus for streak
    points += goal.streak_count * 5;

    return points;
  }

  private generateSpecificSuggestion(goalData: Partial<CreateLearningGoalDto>): string {
    const category = goalData.category;
    const suggestions = {
      [GoalCategory.STUDY_TIME]: "Study for a specific number of hours per day/week",
      [GoalCategory.COURSE_COMPLETION]: "Complete specific courses or modules",
      [GoalCategory.ASSESSMENT_SCORE]: "Achieve a specific score on assessments",
      [GoalCategory.SKILL_MASTERY]: "Master specific clinical skills or competencies",
      [GoalCategory.STREAK_MAINTENANCE]: "Maintain consistent daily study habits",
    };
    
    return suggestions[category as GoalCategory] || "Define exactly what you want to achieve";
  }

  private generateMeasurableSuggestion(goalData: Partial<CreateLearningGoalDto>): string {
    const targetCriteria = goalData.target_criteria;
    if (targetCriteria?.type === 'numeric') {
      return `Track progress towards ${targetCriteria.target_value} ${targetCriteria.unit || 'units'}`;
    }
    return "Define how you will measure progress with specific numbers or percentages";
  }

  private generateAchievableSuggestion(goalData: Partial<CreateLearningGoalDto>): string {
    return "Ensure the goal is challenging but realistic based on your current schedule and commitments";
  }

  private generateRelevantSuggestion(goalData: Partial<CreateLearningGoalDto>): string {
    const category = goalData.category;
    const suggestions = {
      [GoalCategory.USMLE_STEP1]: "Aligns with USMLE Step 1 preparation timeline",
      [GoalCategory.CLINICAL_SKILLS]: "Supports clinical competency development",
      [GoalCategory.SPECIALTY_PREP]: "Relevant to your chosen medical specialty",
    };
    
    return suggestions[category as GoalCategory] || "Ensure this goal supports your broader medical education objectives";
  }

  private generateTimeBoundSuggestion(goalData: Partial<CreateLearningGoalDto>): string {
    const startDate = goalData.start_date;
    const targetDate = goalData.target_date;
    
    if (startDate && targetDate) {
      const days = Math.ceil((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      return `Complete within ${days} days (${Math.ceil(days / 7)} weeks)`;
    }
    
    return "Set a specific deadline for achieving this goal";
  }
}
