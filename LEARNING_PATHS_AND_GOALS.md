# Learning Paths and Goals System

## Overview

The Learning Paths and Goals System is a comprehensive feature that provides structured learning journeys and personalized goal management for medical education. This system helps students organize their studies, track progress, and achieve their educational objectives through guided pathways and SMART goals.

## Features

### Learning Paths
- **Structured Learning Journeys**: Multi-phase learning paths with organized modules
- **Adaptive Recommendations**: AI-powered path suggestions based on user behavior and performance
- **Progress Tracking**: Real-time progress monitoring with detailed analytics
- **Milestone Celebrations**: Achievement recognition with rewards and badges
- **Integration**: Seamless integration with existing courses, assessments, and clinical cases

### Learning Goals
- **SMART Goal Framework**: Specific, Measurable, Achievable, Relevant, Time-bound goals
- **Multiple Goal Types**: Study time, course completion, assessment scores, skill mastery, streaks
- **Progress Tracking**: Automatic and manual progress updates with detailed analytics
- **Milestone Management**: Break down large goals into manageable milestones
- **Reminders & Notifications**: Customizable reminder system to keep users on track

## Architecture

### Backend Components

#### Entities
- `LearningPath`: Core learning path structure with phases and modules
- `LearningPathProgress`: User progress tracking for learning paths
- `LearningPathMilestone`: Achievement milestones within learning paths
- `LearningGoal`: User-defined learning objectives
- `LearningGoalProgress`: Progress tracking for individual goals

#### Services
- `LearningPathsService`: Core learning path management
- `LearningPathProgressService`: Progress tracking and updates
- `LearningPathRecommendationsService`: AI-powered recommendations
- `LearningPathIntegrationService`: Integration with existing systems
- `LearningPathAnalyticsService`: Analytics and insights
- `LearningGoalsService`: Goal management and tracking

#### Controllers
- `LearningPathsController`: REST API endpoints for learning paths
- `LearningGoalsController`: REST API endpoints for goals

### Frontend Components

#### Learning Paths
- `LearningPathInterface`: Main interface for browsing and managing paths
- `LearningPathVisualization`: Visual progress tracking and path navigation
- `LearningPathRecommendations`: Personalized path recommendations
- `MilestoneCelebration`: Achievement celebration modals

#### Goals
- `GoalsManagementInterface`: Main goals dashboard
- `GoalCreationWizard`: Step-by-step goal creation process
- `GoalsProgressWidget`: Dashboard widget for goal overview

#### Analytics
- `LearningPathAnalytics`: Comprehensive analytics dashboard
- `LearningPathProgressWidget`: Dashboard integration
- `GoalsProgressWidget`: Goals overview widget

## API Endpoints

### Learning Paths
```
GET    /learning-paths                    # Get all learning paths
POST   /learning-paths                    # Create new learning path
GET    /learning-paths/:id                # Get specific learning path
PATCH  /learning-paths/:id                # Update learning path
DELETE /learning-paths/:id                # Delete learning path

GET    /learning-paths/recommendations    # Get personalized recommendations
GET    /learning-paths/trending           # Get trending paths
GET    /learning-paths/my-progress        # Get user's path progress

POST   /learning-paths/:id/enroll         # Enroll in learning path
PATCH  /learning-paths/:id/progress       # Update path progress
GET    /learning-paths/:id/progress       # Get path progress
POST   /learning-paths/:id/complete       # Mark path as completed

GET    /learning-paths/:id/analytics      # Get path analytics
GET    /learning-paths/analytics/system   # Get system analytics
GET    /learning-paths/analytics/user-insights # Get user insights
```

### Learning Goals
```
GET    /learning-goals                    # Get user's goals
POST   /learning-goals                    # Create new goal
GET    /learning-goals/:id                # Get specific goal
PATCH  /learning-goals/:id                # Update goal
DELETE /learning-goals/:id                # Delete goal

GET    /learning-goals/analytics          # Get goal analytics
POST   /learning-goals/smart-suggestions  # Get SMART criteria suggestions

POST   /learning-goals/:id/progress       # Add progress entry
GET    /learning-goals/:id/progress       # Get progress history
```

## Database Schema

### Learning Paths Tables
- `learning_paths`: Core path information
- `learning_path_progress`: User progress tracking
- `learning_path_milestones`: Achievement milestones

### Learning Goals Tables
- `learning_goals`: Goal definitions
- `learning_goal_progress`: Progress tracking entries

## Integration Points

### Existing Systems
- **Courses**: Learning paths can include course modules
- **Assessments**: Assessments can be part of learning paths and trigger goal updates
- **Clinical Cases**: Clinical cases integrate with both paths and goals
- **User Progress**: Automatic synchronization with existing progress tracking

### Event System
- Course completion events update path progress
- Assessment results trigger goal progress updates
- Path milestone achievements create celebration events
- Goal completions can trigger recommendations

## Setup and Installation

### Backend Setup
1. Ensure all entities are included in your TypeORM configuration
2. Run database migrations to create the new tables
3. Add the new modules to your main app module
4. Seed sample data using the provided seed scripts

### Frontend Setup
1. Add the new components to your routing configuration
2. Update navigation to include Learning Paths and Goals
3. Integrate widgets into the main dashboard
4. Configure API routes for backend communication

### Database Seeding
```bash
# Run the seed script to populate sample data
npm run seed:learning-paths-goals
```

## Usage Examples

### Creating a Learning Path
```typescript
const newPath = await learningPathsService.create({
  title: 'USMLE Step 1 Preparation',
  description: 'Comprehensive Step 1 prep course',
  category: LearningPathCategory.USMLE_STEP1,
  difficulty: LearningPathDifficulty.INTERMEDIATE,
  estimated_duration_weeks: 16,
  path_structure: {
    phases: [
      {
        title: 'Foundation Review',
        modules: [
          {
            title: 'Anatomy Review',
            type: 'course',
            resource_id: 'anatomy-course-id'
          }
        ]
      }
    ]
  }
}, userId);
```

### Creating a Learning Goal
```typescript
const newGoal = await learningGoalsService.create({
  title: 'Daily Study Goal',
  description: 'Study 6 hours daily for USMLE prep',
  type: GoalType.STUDY_TIME,
  category: GoalCategory.STUDY_TIME,
  priority: GoalPriority.HIGH,
  target_criteria: {
    type: 'numeric',
    target_value: 180, // 6 hours * 30 days
    unit: 'hours'
  },
  start_date: new Date(),
  target_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
}, userId);
```

### Tracking Progress
```typescript
// Update learning path progress
await progressService.updateProgress(userId, pathId, {
  phase_id: 'phase_1',
  module_id: 'module_1',
  progress_percentage: 75,
  time_spent_minutes: 120
});

// Add goal progress entry
await goalsService.addProgressEntry(goalId, userId, {
  entry_type: ProgressEntryType.MANUAL,
  progress_value: 25,
  notes: 'Completed 5 hours of study today'
});
```

## Analytics and Insights

### Path Analytics
- Enrollment and completion rates
- User engagement metrics
- Bottleneck identification
- Milestone achievement rates

### Goal Analytics
- Achievement rates by category
- Streak tracking
- Consistency scoring
- Personalized insights

### User Insights
- Learning velocity analysis
- Preferred difficulty levels
- Strongest subject areas
- Improvement recommendations

## Best Practices

### Learning Path Design
1. Structure paths with clear phases and logical progression
2. Include varied content types (courses, assessments, cases)
3. Set realistic time estimates
4. Create meaningful milestones with appropriate rewards

### Goal Setting
1. Follow SMART criteria for all goals
2. Break large goals into smaller milestones
3. Set appropriate reminder frequencies
4. Enable both automatic and manual tracking when possible

### Integration
1. Use event-driven updates for seamless integration
2. Maintain data consistency across systems
3. Provide fallback mechanisms for failed integrations
4. Monitor integration performance and errors

## Future Enhancements

### Planned Features
- Advanced AI recommendations using machine learning
- Social learning features (study groups, peer comparisons)
- Gamification elements (leaderboards, competitions)
- Mobile app integration
- Advanced analytics with predictive insights

### Extensibility
- Plugin system for custom goal types
- API for third-party integrations
- Customizable milestone reward systems
- Advanced reporting and export capabilities

## Support and Maintenance

### Monitoring
- Track system performance and usage metrics
- Monitor integration health and error rates
- Analyze user engagement and satisfaction

### Updates
- Regular content updates for learning paths
- Algorithm improvements for recommendations
- User interface enhancements based on feedback
- Performance optimizations

For technical support or feature requests, please refer to the main project documentation or contact the development team.
