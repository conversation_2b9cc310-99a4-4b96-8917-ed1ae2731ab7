'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  Route, 
  TrendingUp, 
  Clock, 
  Target, 
  ChevronRight,
  Play,
  CheckCircle,
  Award
} from 'lucide-react';

interface LearningPathProgress {
  id: string;
  overall_progress_percentage: number;
  status: string;
  started_at: string;
  last_accessed_at: string;
  learning_path: {
    id: string;
    title: string;
    category: string;
    estimated_duration_weeks: number;
    milestones: any[];
  };
  milestones_achieved: any[];
}

export const LearningPathProgressWidget: React.FC = () => {
  const [pathProgress, setPathProgress] = useState<LearningPathProgress[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPathProgress();
  }, []);

  const fetchPathProgress = async () => {
    try {
      const response = await fetch('/api/learning-paths/my-progress');
      const data = await response.json();
      setPathProgress(data.slice(0, 3)); // Show top 3 active paths
    } catch (error) {
      console.error('Error fetching learning path progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks}w ago`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Route className="w-5 h-5 text-blue-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Learning Paths</h3>
        </div>
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-100 rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Route className="w-5 h-5 text-blue-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">Learning Paths</h3>
        </div>
        <Link 
          href="/learning-paths"
          className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1"
        >
          <span>View All</span>
          <ChevronRight className="w-4 h-4" />
        </Link>
      </div>

      {pathProgress.length === 0 ? (
        <div className="text-center py-8">
          <Route className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">No Active Learning Paths</h4>
          <p className="text-gray-500 mb-4">
            Start your structured learning journey with curated educational paths
          </p>
          <Link
            href="/learning-paths"
            className="inline-flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Route className="w-4 h-4" />
            <span>Browse Learning Paths</span>
          </Link>
        </div>
      ) : (
        <div className="space-y-4">
          {pathProgress.map((progress) => (
            <Link
              key={progress.id}
              href={`/learning-paths/${progress.learning_path.id}`}
              className="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">
                    {progress.learning_path.title}
                  </h4>
                  <div className="flex items-center space-x-3 text-sm text-gray-500">
                    <span className="capitalize">
                      {progress.learning_path.category.replace('_', ' ')}
                    </span>
                    <span>•</span>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{progress.learning_path.estimated_duration_weeks}w</span>
                    </div>
                    <span>•</span>
                    <span>Last active {formatTimeAgo(progress.last_accessed_at)}</span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(progress.status)}`}>
                    {progress.status.replace('_', ' ')}
                  </span>
                  
                  {progress.status === 'completed' ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <Play className="w-5 h-5 text-blue-600" />
                  )}
                </div>
              </div>

              <div className="mb-3">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                  <span>Progress</span>
                  <span>{Math.round(progress.overall_progress_percentage)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      progress.status === 'completed' ? 'bg-green-600' : 'bg-blue-600'
                    }`}
                    style={{ width: `${progress.overall_progress_percentage}%` }}
                  />
                </div>
              </div>

              {progress.milestones_achieved.length > 0 && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Award className="w-4 h-4 text-yellow-600" />
                  <span>
                    {progress.milestones_achieved.length} of {progress.learning_path.milestones?.length || 0} milestones achieved
                  </span>
                </div>
              )}
            </Link>
          ))}

          <div className="pt-2 border-t border-gray-100">
            <Link
              href="/learning-paths"
              className="flex items-center justify-center space-x-2 text-blue-600 hover:text-blue-700 text-sm font-medium py-2"
            >
              <TrendingUp className="w-4 h-4" />
              <span>Explore More Learning Paths</span>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};
