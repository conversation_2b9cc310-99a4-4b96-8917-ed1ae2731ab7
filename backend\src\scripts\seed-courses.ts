import { DataSource } from 'typeorm';
import { Course, CourseDifficulty, CourseStatus } from '../entities/course.entity';
import { CourseCategory } from '../entities/course-category.entity';
import { User, UserRole } from '../entities/user.entity';

// Sample course categories
const categories = [
  {
    name: 'Anatomy & Physiology',
    slug: 'anatomy-physiology',
    description: 'Study of human body structure and function',
    color: '#3B82F6',
    icon: 'body',
  },
  {
    name: 'Pharmacology',
    slug: 'pharmacology',
    description: 'Study of drugs and their effects',
    color: '#10B981',
    icon: 'pill',
  },
  {
    name: 'Pathology',
    slug: 'pathology',
    description: 'Study of disease processes',
    color: '#EF4444',
    icon: 'microscope',
  },
  {
    name: 'Clinical Medicine',
    slug: 'clinical-medicine',
    description: 'Practical medical diagnosis and treatment',
    color: '#8B5CF6',
    icon: 'stethoscope',
  },
  {
    name: 'Surgery',
    slug: 'surgery',
    description: 'Surgical procedures and techniques',
    color: '#F59E0B',
    icon: 'scalpel',
  },
];

// Sample courses
const courses = [
  {
    title: 'Introduction to Human Anatomy',
    description: 'Comprehensive introduction to human body systems, organs, and structures. Perfect for medical students beginning their journey.',
    short_description: 'Learn the fundamentals of human anatomy with interactive 3D models and detailed explanations.',
    code: 'ANAT101',
    difficulty: CourseDifficulty.BEGINNER,
    estimated_hours: 40,
    credit_hours: 3,
    category_slug: 'anatomy-physiology',
    tags: ['anatomy', 'basic', 'foundation'],
    learning_objectives: [
      'Identify major body systems and their components',
      'Understand anatomical terminology and directional references',
      'Describe the structure and function of organs',
      'Apply anatomical knowledge to clinical scenarios'
    ],
    is_public: true,
    is_featured: true,
    price: 0,
  },
  {
    title: 'Advanced Cardiovascular Physiology',
    description: 'Deep dive into cardiovascular system function, including cardiac cycle, blood pressure regulation, and pathophysiology.',
    short_description: 'Master cardiovascular physiology with advanced concepts and clinical applications.',
    code: 'PHYS301',
    difficulty: CourseDifficulty.ADVANCED,
    estimated_hours: 60,
    credit_hours: 4,
    category_slug: 'anatomy-physiology',
    tags: ['physiology', 'cardiovascular', 'advanced'],
    learning_objectives: [
      'Analyze cardiac cycle mechanics and electrical conduction',
      'Evaluate blood pressure regulation mechanisms',
      'Interpret cardiovascular diagnostic tests',
      'Apply knowledge to cardiovascular pathologies'
    ],
    prerequisites: {
      course_ids: [], // Will be filled with ANAT101 ID after creation
      skills: ['Basic anatomy knowledge', 'Understanding of cell biology'],
      description: 'Students must complete Introduction to Human Anatomy before enrolling'
    },
    is_public: true,
    is_featured: true,
    price: 99.99,
  },
  {
    title: 'Pharmacology Fundamentals',
    description: 'Essential principles of pharmacology including drug mechanisms, pharmacokinetics, and therapeutic applications.',
    short_description: 'Learn how drugs work in the human body and their clinical applications.',
    code: 'PHAR201',
    difficulty: CourseDifficulty.INTERMEDIATE,
    estimated_hours: 50,
    credit_hours: 3,
    category_slug: 'pharmacology',
    tags: ['pharmacology', 'drugs', 'mechanisms'],
    learning_objectives: [
      'Understand drug absorption, distribution, metabolism, and excretion',
      'Explain drug-receptor interactions and mechanisms',
      'Calculate drug dosages and therapeutic indices',
      'Identify drug interactions and adverse effects'
    ],
    prerequisites: {
      course_ids: [], // Will be filled with ANAT101 ID
      skills: ['Basic chemistry', 'Human physiology'],
      description: 'Basic understanding of human anatomy and chemistry required'
    },
    is_public: true,
    is_featured: false,
    price: 79.99,
  },
  {
    title: 'Clinical Pathology Essentials',
    description: 'Study of disease processes, diagnostic methods, and pathological changes in human tissues.',
    short_description: 'Understand disease mechanisms and diagnostic pathology.',
    code: 'PATH301',
    difficulty: CourseDifficulty.ADVANCED,
    estimated_hours: 70,
    credit_hours: 4,
    category_slug: 'pathology',
    tags: ['pathology', 'disease', 'diagnosis'],
    learning_objectives: [
      'Identify pathological changes in tissues',
      'Understand disease mechanisms and progression',
      'Interpret laboratory and imaging findings',
      'Correlate pathology with clinical presentations'
    ],
    prerequisites: {
      course_ids: [], // Will be filled with prerequisite IDs
      skills: ['Advanced anatomy', 'Physiology knowledge'],
      description: 'Advanced anatomy and physiology knowledge required'
    },
    is_public: true,
    is_featured: true,
    price: 129.99,
  },
  {
    title: 'Basic Surgical Techniques',
    description: 'Introduction to fundamental surgical principles, sterile technique, and basic procedures.',
    short_description: 'Learn essential surgical skills and techniques.',
    code: 'SURG201',
    difficulty: CourseDifficulty.INTERMEDIATE,
    estimated_hours: 80,
    credit_hours: 5,
    category_slug: 'surgery',
    tags: ['surgery', 'techniques', 'procedures'],
    learning_objectives: [
      'Master sterile technique and surgical safety',
      'Perform basic surgical procedures',
      'Understand wound healing and post-operative care',
      'Apply surgical principles to clinical scenarios'
    ],
    prerequisites: {
      course_ids: [], // Will be filled with prerequisite IDs
      skills: ['Advanced anatomy', 'Clinical experience'],
      description: 'Advanced anatomy knowledge and some clinical experience required'
    },
    is_public: true,
    is_featured: false,
    price: 199.99,
  },
];

export async function seedCourses(dataSource: DataSource) {
  const categoryRepository = dataSource.getRepository(CourseCategory);
  const courseRepository = dataSource.getRepository(Course);
  const userRepository = dataSource.getRepository(User);

  console.log('🌱 Seeding course categories...');

  // Create categories
  const createdCategories = [];
  for (const categoryData of categories) {
    const existingCategory = await categoryRepository.findOne({
      where: { slug: categoryData.slug }
    });

    if (!existingCategory) {
      const category = categoryRepository.create(categoryData);
      const savedCategory = await categoryRepository.save(category);
      createdCategories.push(savedCategory);
      console.log(`✅ Created category: ${savedCategory.name}`);
    } else {
      createdCategories.push(existingCategory);
      console.log(`⏭️  Category already exists: ${existingCategory.name}`);
    }
  }

  console.log('🌱 Seeding courses...');

  // Find or create instructor user
  let instructor = await userRepository.findOne({
    where: { email: '<EMAIL>' }
  });

  if (!instructor) {
    instructor = userRepository.create({
      email: '<EMAIL>',
      name: 'Dr. Sarah Johnson',
      role: UserRole.TEACHER,
      is_verified: true,
    });
    instructor = await userRepository.save(instructor);
    console.log('✅ Created instructor user');
  }

  // Create courses
  const createdCourses = [];
  for (const courseData of courses) {
    const existingCourse = await courseRepository.findOne({
      where: { code: courseData.code }
    });

    if (!existingCourse) {
      const category = createdCategories.find(c => c.slug === courseData.category_slug);
      
      const course = courseRepository.create({
        ...courseData,
        category_id: category?.id,
        instructor_id: instructor.id,
        status: CourseStatus.PUBLISHED,
        rating: Math.random() * 2 + 3, // Random rating between 3-5
        rating_count: Math.floor(Math.random() * 100) + 10,
        enrollment_count: Math.floor(Math.random() * 500) + 50,
      });

      const savedCourse = await courseRepository.save(course);
      createdCourses.push(savedCourse);
      console.log(`✅ Created course: ${savedCourse.title}`);
    } else {
      createdCourses.push(existingCourse);
      console.log(`⏭️  Course already exists: ${existingCourse.title}`);
    }
  }

  // Update prerequisites with actual course IDs
  console.log('🔗 Setting up course prerequisites...');
  
  const anatomyCourse = createdCourses.find(c => c.code === 'ANAT101');
  const physiologyCourse = createdCourses.find(c => c.code === 'PHYS301');
  const pharmacologyCourse = createdCourses.find(c => c.code === 'PHAR201');
  const pathologyCourse = createdCourses.find(c => c.code === 'PATH301');
  const surgeryCourse = createdCourses.find(c => c.code === 'SURG201');

  if (anatomyCourse && physiologyCourse) {
    physiologyCourse.prerequisites.course_ids = [anatomyCourse.id];
    await courseRepository.save(physiologyCourse);
    console.log('✅ Set prerequisites for Advanced Cardiovascular Physiology');
  }

  if (anatomyCourse && pharmacologyCourse) {
    pharmacologyCourse.prerequisites.course_ids = [anatomyCourse.id];
    await courseRepository.save(pharmacologyCourse);
    console.log('✅ Set prerequisites for Pharmacology Fundamentals');
  }

  if (anatomyCourse && physiologyCourse && pathologyCourse) {
    pathologyCourse.prerequisites.course_ids = [anatomyCourse.id, physiologyCourse.id];
    await courseRepository.save(pathologyCourse);
    console.log('✅ Set prerequisites for Clinical Pathology Essentials');
  }

  if (anatomyCourse && surgeryCourse) {
    surgeryCourse.prerequisites.course_ids = [anatomyCourse.id];
    await courseRepository.save(surgeryCourse);
    console.log('✅ Set prerequisites for Basic Surgical Techniques');
  }

  console.log('🎉 Course seeding completed!');
  console.log(`📊 Created ${createdCategories.length} categories and ${createdCourses.length} courses`);
}
